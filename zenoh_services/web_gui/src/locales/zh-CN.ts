// 中文本地化配置
export const zhCN = {
  // 通用
  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    add: '添加',
    search: '搜索',
    refresh: '刷新',
    loading: '加载中...',
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息',
  },

  // 菜单
  menu: {
    dashboard: '仪表盘',
    training: '模型训练',
    simulation: '仿真环境',
    deployment: '模型部署',
    monitoring: '系统监控',
    settings: '系统设置',
  },

  // 状态
  status: {
    online: '在线',
    offline: '离线',
    running: '运行中',
    stopped: '已停止',
    paused: '已暂停',
    completed: '已完成',
    failed: '失败',
    connecting: '连接中',
    connected: '已连接',
    disconnected: '未连接',
  },

  // 训练相关
  training: {
    startTraining: '开始训练',
    stopTraining: '停止训练',
    pauseTraining: '暂停训练',
    resumeTraining: '恢复训练',
    trainingProgress: '训练进度',
    trainingJobs: '训练任务',
    trainingConfig: '训练配置',
    modelName: '模型名称',
    episodes: '训练轮次',
    learningRate: '学习率',
    batchSize: '批次大小',
  },

  // 系统监控
  monitoring: {
    systemMetrics: '系统指标',
    cpuUsage: 'CPU使用率',
    gpuUsage: 'GPU使用率',
    memoryUsage: '内存使用率',
    networkTraffic: '网络流量',
    diskUsage: '磁盘使用率',
  },

  // 仿真相关
  simulation: {
    startSimulation: '开始仿真',
    stopSimulation: '停止仿真',
    simulationConfig: '仿真配置',
    environment: '环境',
    robotModel: '机器人模型',
    physics: '物理引擎',
  },

  // 部署相关
  deployment: {
    deployModel: '部署模型',
    modelList: '模型列表',
    deploymentStatus: '部署状态',
    version: '版本',
    environment: '环境',
  },
};

export default zhCN;