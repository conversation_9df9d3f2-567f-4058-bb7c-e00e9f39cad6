import React from 'react';
import { ConfigProvider, Layout, theme } from 'antd';
import MainLayout from './components/layout/MainLayout';
import './App.css';

const { darkAlgorithm } = theme;

const App: React.FC = () => {
  return (
    <ConfigProvider
      theme={{
        algorithm: darkAlgorithm,
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
        },
      }}
    >
      <Layout style={{ minHeight: '100vh' }}>
        <MainLayout />
      </Layout>
    </ConfigProvider>
  );
};

export default App;
