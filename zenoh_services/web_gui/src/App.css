/* Global Styles for PC Browser Optimization */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #0a0a0a;
  color: #ffffff;
  overflow-x: hidden;
}

#root {
  width: 100vw;
  margin: 0;
  padding: 0;
  text-align: left;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
}

/* Enhanced Layout Styles for PC */
.dashboard-card {
  margin-bottom: 32px;
  border-radius: 16px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
}

.dashboard-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.4),
    0 4px 16px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dashboard-card .ant-card-body {
  padding: 32px;
  background: transparent;
}

.metric-card {
  text-align: center;
  padding: 32px 24px;
  border-radius: 16px;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.08), rgba(24, 144, 255, 0.02));
  border: 1px solid rgba(24, 144, 255, 0.15);
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 0%, rgba(24, 144, 255, 0.1), transparent 70%);
  pointer-events: none;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-healthy {
  background: rgba(82, 196, 26, 0.15);
  color: #52c41a;
  border: 1px solid rgba(82, 196, 26, 0.3);
}

.status-warning {
  background: rgba(250, 173, 20, 0.15);
  color: #faad14;
  border: 1px solid rgba(250, 173, 20, 0.3);
}

.status-critical {
  background: rgba(255, 77, 79, 0.15);
  color: #ff4d4f;
  border: 1px solid rgba(255, 77, 79, 0.3);
}

.large-number {
  font-size: 32px;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.2;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.section-title {
  font-size: 28px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 32px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.section-title .anticon {
  font-size: 32px;
  color: #1890ff;
}

/* Enhanced Table Styles for PC */
.ant-table {
  background: transparent !important;
}

.ant-table-thead > tr > th {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.15) 0%, rgba(24, 144, 255, 0.08) 100%) !important;
  border-bottom: 2px solid rgba(24, 144, 255, 0.4) !important;
  color: #ffffff !important;
  font-weight: 700 !important;
  font-size: 16px !important;
  padding: 24px 20px !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
}

.ant-table-thead > tr > th::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(24, 144, 255, 0.5), transparent);
}

.ant-table-tbody > tr > td {
  border-bottom: 1px solid rgba(255, 255, 255, 0.08) !important;
  padding: 20px !important;
  font-size: 15px !important;
  background: rgba(0, 0, 0, 0.2) !important;
  transition: all 0.3s ease;
}

.ant-table-tbody > tr:hover > td {
  background: rgba(24, 144, 255, 0.08) !important;
  transform: scale(1.01);
}

.ant-table-tbody > tr {
  transition: all 0.3s ease;
}

.ant-table-tbody > tr:hover {
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);
}

/* Enhanced Card Styles for PC */
.ant-card {
  background: rgba(20, 20, 20, 0.8) !important;
  border: 1px solid rgba(255, 255, 255, 0.08) !important;
  border-radius: 16px !important;
  backdrop-filter: blur(20px);
  position: relative;
}

.ant-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.02) 0%, transparent 50%, rgba(255,255,255,0.01) 100%);
  border-radius: 16px;
  pointer-events: none;
}

.ant-card-head {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  padding: 0 32px !important;
  min-height: 80px !important;
  background: rgba(0, 0, 0, 0.2) !important;
  border-radius: 16px 16px 0 0 !important;
  position: relative;
  z-index: 1;
}

.ant-card-head-title {
  font-size: 22px !important;
  font-weight: 700 !important;
  color: #ffffff !important;
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
}

.ant-card-body {
  padding: 32px !important;
  position: relative;
  z-index: 1;
}

/* Enhanced Progress Circles */
.progress-circle-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
}

.progress-value {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  margin-top: 16px;
}

.progress-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* Service Status Improvements */
.service-status-card {
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.2));
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.service-status-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  border-color: rgba(24, 144, 255, 0.3);
}

.service-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.service-metric:last-child {
  border-bottom: none;
}

.service-metric-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.service-metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

/* Alert Improvements */
.alert-item {
  padding: 24px;
  margin-bottom: 16px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  border-left: 4px solid;
}

.alert-critical {
  border-left-color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
}

.alert-warning {
  border-left-color: #faad14;
  background: rgba(250, 173, 20, 0.1);
}

.alert-info {
  border-left-color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

/* Enhanced Menu Styles */
.ant-menu-dark {
  background: transparent !important;
}

.ant-menu-dark .ant-menu-item {
  background: transparent !important;
  border-radius: 12px !important;
  margin: 8px 16px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
}

.ant-menu-dark .ant-menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(24, 144, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.ant-menu-dark .ant-menu-item:hover::before {
  left: 100%;
}

.ant-menu-dark .ant-menu-item:hover {
  background: rgba(24, 144, 255, 0.1) !important;
  transform: translateX(8px);
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);
}

.ant-menu-dark .ant-menu-item-selected {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.2) 0%, rgba(24, 144, 255, 0.1) 100%) !important;
  border: 1px solid rgba(24, 144, 255, 0.3) !important;
  box-shadow:
    0 4px 16px rgba(24, 144, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Enhanced Button Styles */
.ant-btn {
  border-radius: 8px !important;
  font-weight: 600 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
}

.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%) !important;
  border: none !important;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3) !important;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.4) !important;
}

.ant-btn:not(.ant-btn-primary) {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

.ant-btn:not(.ant-btn-primary):hover {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
  transform: translateY(-1px) !important;
}

/* Responsive Layout for PC-first design */
@media (min-width: 1920px) {
  .ant-card-body {
    padding: 40px !important;
  }

  .large-number {
    font-size: 42px;
  }

  .section-title {
    font-size: 36px;
  }
}

@media (max-width: 1600px) {
  .ant-card-body {
    padding: 28px !important;
  }

  .large-number {
    font-size: 32px;
  }

  .section-title {
    font-size: 28px;
  }
}

@media (max-width: 1200px) {
  .ant-card-body {
    padding: 24px !important;
  }

  .large-number {
    font-size: 28px;
  }

  .section-title {
    font-size: 24px;
  }
}

@media (max-width: 768px) {
  .ant-card-body {
    padding: 20px !important;
  }

  .large-number {
    font-size: 24px;
  }

  .metric-label {
    font-size: 14px;
  }

  .section-title {
    font-size: 20px;
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.6), rgba(24, 144, 255, 0.3));
  border-radius: 4px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.8), rgba(24, 144, 255, 0.5));
}

/* Selection Styling */
::selection {
  background: rgba(24, 144, 255, 0.3);
  color: #ffffff;
}

::-moz-selection {
  background: rgba(24, 144, 255, 0.3);
  color: #ffffff;
}
