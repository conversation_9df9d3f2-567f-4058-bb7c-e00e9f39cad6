/**
 * Zenoh WebSocket Client with connection management and reconnection logic
 */

import { 
  StandardZenohMessage, 
  ZenohConnectionStatus, 
  ConnectionState, 
  Subscription,
  ZenohClientConfig,
  MessageType
} from '../types/zenoh-types';

export class ZenohWebSocketClient {
  private ws: WebSocket | null = null;
  private config: ZenohClientConfig;
  private subscriptions = new Map<string, Subscription>();
  private connectionStatus: ZenohConnectionStatus;
  private reconnectTimeoutId: number | null = null;
  private heartbeatIntervalId: number | null = null;
  private messageQueue: StandardZenohMessage[] = [];
  private statusCallbacks = new Set<(status: ZenohConnectionStatus) => void>();

  constructor(config: Partial<ZenohClientConfig> = {}) {
    this.config = {
      url: 'ws://localhost:8080/ws',  // Default Zenoh WebSocket endpoint
      reconnectInterval: 5000,       // 5 seconds
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,      // 30 seconds
      ...config
    };

    this.connectionStatus = {
      state: ConnectionState.DISCONNECTED,
      reconnectAttempts: 0
    };
  }

  /**
   * Connect to Zenoh router via WebSocket
   */
  async connect(): Promise<void> {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return; // Already connected
    }

    this.updateConnectionStatus({
      state: ConnectionState.CONNECTING,
      error: undefined
    });

    try {
      this.ws = new WebSocket(this.config.url);
      
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onerror = this.handleError.bind(this) as (event: Event) => void;
      this.ws.onclose = this.handleClose.bind(this);

    } catch (error) {
      this.handleError(new ErrorEvent('connection', { 
        error: error instanceof Error ? error : new Error(String(error))
      }));
    }
  }

  /**
   * Disconnect from Zenoh router
   */
  disconnect(): void {
    this.clearTimeouts();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnecting');
      this.ws = null;
    }

    this.updateConnectionStatus({
      state: ConnectionState.DISCONNECTED,
      error: undefined
    });
  }

  /**
   * Subscribe to a Zenoh topic
   */
  subscribe(
    topic: string, 
    callback: (message: StandardZenohMessage) => void
  ): string {
    const subscriptionId = `${topic}_${Date.now()}_${Math.random()}`;
    
    const subscription: Subscription = {
      topic,
      callback,
      id: subscriptionId
    };

    this.subscriptions.set(subscriptionId, subscription);

    // If connected, send subscription request immediately
    if (this.isConnected()) {
      this.sendSubscriptionRequest(topic, subscriptionId, 'subscribe');
    }

    return subscriptionId;
  }

  /**
   * Unsubscribe from a topic
   */
  unsubscribe(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      if (this.isConnected()) {
        this.sendSubscriptionRequest(subscription.topic, subscriptionId, 'unsubscribe');
      }
      this.subscriptions.delete(subscriptionId);
    }
  }

  /**
   * Publish a message to a topic
   */
  publish(topic: string, message: StandardZenohMessage): void {
    const publishMessage = {
      type: 'publish',
      topic,
      data: message
    };

    this.sendMessage(publishMessage);
  }

  /**
   * Send a command message
   */
  sendCommand(topic: string, command: string, parameters: Record<string, any> = {}): void {
    const message: StandardZenohMessage = {
      header: {
        message_id: this.generateId(),
        timestamp: Date.now() / 1000,
        message_type: MessageType.COMMAND,
        priority: 1,
        source_service: 'web_gui',
        destination_service: '',
        correlation_id: '',
        ttl: 300
      },
      payload: {
        command,
        parameters
      },
      metadata: {}
    };

    this.publish(topic, message);
  }

  /**
   * Add a connection status listener
   */
  onConnectionStatusChanged(callback: (status: ZenohConnectionStatus) => void): () => void {
    this.statusCallbacks.add(callback);
    
    // Immediately call with current status
    callback(this.connectionStatus);
    
    // Return cleanup function
    return () => {
      this.statusCallbacks.delete(callback);
    };
  }

  /**
   * Get current connection status
   */
  getConnectionStatus(): ZenohConnectionStatus {
    return { ...this.connectionStatus };
  }

  /**
   * Check if client is connected
   */
  isConnected(): boolean {
    return this.connectionStatus.state === ConnectionState.CONNECTED;
  }

  // Private methods

  private handleOpen(): void {
    console.log('Zenoh WebSocket connected');
    
    this.updateConnectionStatus({
      state: ConnectionState.CONNECTED,
      lastConnected: new Date(),
      reconnectAttempts: 0,
      error: undefined
    });

    // Send queued messages
    this.flushMessageQueue();

    // Resubscribe to all topics
    this.resubscribeAll();

    // Start heartbeat
    this.startHeartbeat();
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);
      
      if (data.type === 'message' && data.topic && data.data) {
        this.handleIncomingMessage(data.topic, data.data);
      } else if (data.type === 'subscription_confirmed') {
        console.log(`Subscription confirmed for topic: ${data.topic}`);
      } else if (data.type === 'error') {
        console.error('Zenoh error:', data.error);
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }

  private handleError(event: ErrorEvent): void {
    console.error('Zenoh WebSocket error:', event.error);
    
    this.updateConnectionStatus({
      state: ConnectionState.ERROR,
      error: event.error?.message || 'WebSocket connection error'
    });
  }

  private handleClose(event: CloseEvent): void {
    console.log('Zenoh WebSocket closed:', event.code, event.reason);
    
    this.clearTimeouts();
    
    if (event.code !== 1000) { // Not a normal closure
      this.attemptReconnect();
    } else {
      this.updateConnectionStatus({
        state: ConnectionState.DISCONNECTED,
        error: undefined
      });
    }
  }

  private handleIncomingMessage(topic: string, messageData: StandardZenohMessage): void {
    // Find all subscriptions for this topic
    for (const subscription of this.subscriptions.values()) {
      if (this.topicMatches(subscription.topic, topic)) {
        try {
          subscription.callback(messageData);
        } catch (error) {
          console.error('Error in subscription callback:', error);
        }
      }
    }
  }

  private topicMatches(subscriptionTopic: string, messageTopic: string): boolean {
    // Simple exact match for now - could be extended to support wildcards
    return subscriptionTopic === messageTopic;
  }

  private attemptReconnect(): void {
    if (this.connectionStatus.reconnectAttempts >= this.config.maxReconnectAttempts) {
      this.updateConnectionStatus({
        state: ConnectionState.ERROR,
        error: 'Max reconnection attempts reached'
      });
      return;
    }

    this.updateConnectionStatus({
      state: ConnectionState.RECONNECTING,
      reconnectAttempts: this.connectionStatus.reconnectAttempts + 1
    });

    this.reconnectTimeoutId = window.setTimeout(() => {
      this.connect();
    }, this.config.reconnectInterval);
  }

  private sendSubscriptionRequest(topic: string, subscriptionId: string, action: 'subscribe' | 'unsubscribe'): void {
    const request = {
      type: action,
      topic,
      subscription_id: subscriptionId
    };

    this.sendMessage(request);
  }

  private sendMessage(message: any): void {
    if (this.isConnected() && this.ws) {
      try {
        this.ws.send(JSON.stringify(message));
      } catch (error) {
        console.error('Failed to send WebSocket message:', error);
        // Add to queue for retry
        if (message.type === 'publish') {
          this.messageQueue.push(message.data);
        }
      }
    } else {
      // Queue message for when connection is restored
      if (message.type === 'publish') {
        this.messageQueue.push(message.data);
      }
    }
  }

  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      if (message) {
        // Reconstruct the publish message - this is a simplified approach
        // In a real implementation, you'd want to store the original topic as well
        console.log('Sending queued message:', message);
      }
    }
  }

  private resubscribeAll(): void {
    for (const subscription of this.subscriptions.values()) {
      this.sendSubscriptionRequest(subscription.topic, subscription.id, 'subscribe');
    }
  }

  private startHeartbeat(): void {
    this.heartbeatIntervalId = window.setInterval(() => {
      if (this.isConnected()) {
        const heartbeatMessage = {
          type: 'heartbeat',
          timestamp: Date.now()
        };
        this.sendMessage(heartbeatMessage);
      }
    }, this.config.heartbeatInterval);
  }

  private clearTimeouts(): void {
    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId);
      this.reconnectTimeoutId = null;
    }
    
    if (this.heartbeatIntervalId) {
      clearInterval(this.heartbeatIntervalId);
      this.heartbeatIntervalId = null;
    }
  }

  private updateConnectionStatus(updates: Partial<ZenohConnectionStatus>): void {
    this.connectionStatus = {
      ...this.connectionStatus,
      ...updates
    };

    // Notify all listeners
    for (const callback of this.statusCallbacks) {
      try {
        callback(this.connectionStatus);
      } catch (error) {
        console.error('Error in connection status callback:', error);
      }
    }
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}