import React from 'react';
import { Row, Col, Card, Statistic, Progress, Typography, Space, Timeline, List, Button, Tag, Divider, Alert } from 'antd';
import ReactECharts from 'echarts-for-react';
import {
  RocketOutlined,
  RobotOutlined,
  CloudUploadOutlined,
  MonitorOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  DashboardOutlined,
  TrophyOutlined,
  <PERSON>boltOutlined,
  DatabaseOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
  LineChartOutlined,
  BarChartOutlined,
  PieChartOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

interface DashboardViewProps {}

const DashboardView: React.FC<DashboardViewProps> = () => {
  // Enhanced mock data - will be replaced with real data from Zenoh
  const systemStats = {
    trainingSessions: 12,
    modelsDeployed: 8,
    simulationsRunning: 3,
    uptime: 99.2,
    totalRewards: 15420.5,
    avgSuccessRate: 87.3,
    activeEnvironments: 5,
    totalEpisodes: 45600,
  };

  // Performance metrics for charts
  const performanceData = {
    trainingProgress: [
      { episode: 0, reward: 0, success_rate: 0 },
      { episode: 1000, reward: 150, success_rate: 15 },
      { episode: 2000, reward: 380, success_rate: 35 },
      { episode: 3000, reward: 620, success_rate: 58 },
      { episode: 4000, reward: 850, success_rate: 72 },
      { episode: 5000, reward: 1050, success_rate: 84 },
      { episode: 6000, reward: 1200, success_rate: 87 },
    ],
    systemMetrics: {
      cpu: [45, 52, 48, 61, 55, 67, 59, 63, 58, 71, 65, 69],
      gpu: [78, 82, 85, 79, 88, 91, 86, 89, 92, 87, 90, 85],
      memory: [62, 65, 68, 71, 69, 73, 75, 72, 76, 78, 74, 77],
    }
  };

  const recentActivities = [
    { 
      icon: <RocketOutlined style={{ color: '#52c41a' }} />, 
      title: 'Training completed: anymal_c_rough_terrain',
      timestamp: '2 minutes ago',
      type: 'success'
    },
    { 
      icon: <RobotOutlined style={{ color: '#1890ff' }} />, 
      title: 'Simulation started: flat_terrain_test',
      timestamp: '5 minutes ago',
      type: 'info'
    },
    { 
      icon: <CloudUploadOutlined style={{ color: '#faad14' }} />, 
      title: 'Model deployment in progress',
      timestamp: '10 minutes ago',
      type: 'warning'
    },
    { 
      icon: <MonitorOutlined style={{ color: '#52c41a' }} />, 
      title: 'All services healthy',
      timestamp: '15 minutes ago',
      type: 'success'
    },
  ];

  const serviceStatuses = [
    { name: 'Training Service', status: 'online', uptime: '99.8%' },
    { name: 'Simulation Service', status: 'online', uptime: '99.5%' },
    { name: 'Deployment Service', status: 'online', uptime: '98.9%' },
    { name: 'Configuration Service', status: 'online', uptime: '99.9%' },
    { name: 'Play Service', status: 'maintenance', uptime: '95.2%' },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'maintenance':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#f5222d' }} />;
    }
  };

  // Chart configurations
  const getTrainingProgressChart = () => ({
    title: {
      text: 'Training Progress',
      textStyle: { color: '#ffffff', fontSize: 16, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: '#1890ff',
      textStyle: { color: '#ffffff' }
    },
    legend: {
      data: ['Reward', 'Success Rate'],
      textStyle: { color: '#ffffff' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: performanceData.trainingProgress.map(d => d.episode),
      axisLine: { lineStyle: { color: '#ffffff' } },
      axisLabel: { color: '#ffffff' }
    },
    yAxis: [
      {
        type: 'value',
        name: 'Reward',
        axisLine: { lineStyle: { color: '#ffffff' } },
        axisLabel: { color: '#ffffff' },
        nameTextStyle: { color: '#ffffff' }
      },
      {
        type: 'value',
        name: 'Success Rate (%)',
        axisLine: { lineStyle: { color: '#ffffff' } },
        axisLabel: { color: '#ffffff' },
        nameTextStyle: { color: '#ffffff' }
      }
    ],
    series: [
      {
        name: 'Reward',
        type: 'line',
        data: performanceData.trainingProgress.map(d => d.reward),
        smooth: true,
        lineStyle: { color: '#1890ff', width: 3 },
        itemStyle: { color: '#1890ff' }
      },
      {
        name: 'Success Rate',
        type: 'line',
        yAxisIndex: 1,
        data: performanceData.trainingProgress.map(d => d.success_rate),
        smooth: true,
        lineStyle: { color: '#52c41a', width: 3 },
        itemStyle: { color: '#52c41a' }
      }
    ]
  });

  const getSystemMetricsChart = () => ({
    title: {
      text: 'System Metrics (Last 12 Hours)',
      textStyle: { color: '#ffffff', fontSize: 16, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: '#1890ff',
      textStyle: { color: '#ffffff' }
    },
    legend: {
      data: ['CPU Usage', 'GPU Usage', 'Memory Usage'],
      textStyle: { color: '#ffffff' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: Array.from({length: 12}, (_, i) => `${i+1}h`),
      axisLine: { lineStyle: { color: '#ffffff' } },
      axisLabel: { color: '#ffffff' }
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLine: { lineStyle: { color: '#ffffff' } },
      axisLabel: { color: '#ffffff', formatter: '{value}%' }
    },
    series: [
      {
        name: 'CPU Usage',
        type: 'line',
        data: performanceData.systemMetrics.cpu,
        smooth: true,
        lineStyle: { color: '#faad14', width: 2 },
        areaStyle: { color: 'rgba(250, 173, 20, 0.1)' }
      },
      {
        name: 'GPU Usage',
        type: 'line',
        data: performanceData.systemMetrics.gpu,
        smooth: true,
        lineStyle: { color: '#f5222d', width: 2 },
        areaStyle: { color: 'rgba(245, 34, 45, 0.1)' }
      },
      {
        name: 'Memory Usage',
        type: 'line',
        data: performanceData.systemMetrics.memory,
        smooth: true,
        lineStyle: { color: '#722ed1', width: 2 },
        areaStyle: { color: 'rgba(114, 46, 209, 0.1)' }
      }
    ]
  });

  return (
    <div style={{ background: 'transparent' }}>
      {/* Header Section */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '32px'
      }}>
        <div>
          <Title level={1} style={{
            margin: 0,
            color: '#ffffff',
            fontSize: '32px',
            fontWeight: 700,
            display: 'flex',
            alignItems: 'center',
            gap: '16px'
          }}>
            <DashboardOutlined style={{ color: '#1890ff' }} />
            System Dashboard
          </Title>
          <Text style={{
            color: 'rgba(255,255,255,0.6)',
            fontSize: '16px',
            marginTop: '8px',
            display: 'block'
          }}>
            Real-time monitoring and control center for EngineAI Legged Robot Platform
          </Text>
        </div>
        <Space size="middle">
          <Button type="primary" icon={<PlayCircleOutlined />} size="large">
            Start Training
          </Button>
          <Button icon={<ReloadOutlined />} size="large">
            Refresh
          </Button>
          <Button icon={<SettingOutlined />} size="large">
            Settings
          </Button>
        </Space>
      </div>

      {/* Quick Status Alert */}
      <Alert
        message="System Status: All services operational"
        description="Last updated: 2 minutes ago • Next maintenance: Scheduled for Sunday 2:00 AM"
        type="success"
        showIcon
        style={{
          marginBottom: '32px',
          background: 'rgba(82, 196, 26, 0.1)',
          border: '1px solid rgba(82, 196, 26, 0.3)',
          borderRadius: '8px'
        }}
      />

      {/* Enhanced System Overview Cards */}
      <Row gutter={[32, 32]} style={{ marginBottom: '40px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="dashboard-card"
            style={{
              background: 'linear-gradient(135deg, rgba(24,144,255,0.1) 0%, rgba(24,144,255,0.05) 100%)',
              border: '1px solid rgba(24,144,255,0.2)',
              borderRadius: '12px',
              height: '160px'
            }}
          >
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <RocketOutlined style={{ fontSize: '32px', color: '#1890ff', marginBottom: '16px' }} />
              <div style={{ fontSize: '36px', fontWeight: 700, color: '#ffffff', marginBottom: '8px' }}>
                {systemStats.trainingSessions}
              </div>
              <div style={{ fontSize: '14px', color: 'rgba(255,255,255,0.7)', textTransform: 'uppercase', letterSpacing: '1px' }}>
                Training Sessions
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="dashboard-card"
            style={{
              background: 'linear-gradient(135deg, rgba(82,196,26,0.1) 0%, rgba(82,196,26,0.05) 100%)',
              border: '1px solid rgba(82,196,26,0.2)',
              borderRadius: '12px',
              height: '160px'
            }}
          >
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <TrophyOutlined style={{ fontSize: '32px', color: '#52c41a', marginBottom: '16px' }} />
              <div style={{ fontSize: '36px', fontWeight: 700, color: '#ffffff', marginBottom: '8px' }}>
                {systemStats.modelsDeployed}
              </div>
              <div style={{ fontSize: '14px', color: 'rgba(255,255,255,0.7)', textTransform: 'uppercase', letterSpacing: '1px' }}>
                Models Deployed
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="dashboard-card"
            style={{
              background: 'linear-gradient(135deg, rgba(250,173,20,0.1) 0%, rgba(250,173,20,0.05) 100%)',
              border: '1px solid rgba(250,173,20,0.2)',
              borderRadius: '12px',
              height: '160px'
            }}
          >
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <ThunderboltOutlined style={{ fontSize: '32px', color: '#faad14', marginBottom: '16px' }} />
              <div style={{ fontSize: '36px', fontWeight: 700, color: '#ffffff', marginBottom: '8px' }}>
                {systemStats.simulationsRunning}
              </div>
              <div style={{ fontSize: '14px', color: 'rgba(255,255,255,0.7)', textTransform: 'uppercase', letterSpacing: '1px' }}>
                Active Simulations
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="dashboard-card"
            style={{
              background: 'linear-gradient(135deg, rgba(114,46,209,0.1) 0%, rgba(114,46,209,0.05) 100%)',
              border: '1px solid rgba(114,46,209,0.2)',
              borderRadius: '12px',
              height: '160px'
            }}
          >
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <DatabaseOutlined style={{ fontSize: '32px', color: '#722ed1', marginBottom: '16px' }} />
              <div style={{ fontSize: '36px', fontWeight: 700, color: '#ffffff', marginBottom: '8px' }}>
                {systemStats.uptime}%
              </div>
              <div style={{ fontSize: '14px', color: 'rgba(255,255,255,0.7)', textTransform: 'uppercase', letterSpacing: '1px' }}>
                System Uptime
              </div>
              <Progress
                percent={systemStats.uptime}
                showInfo={false}
                strokeColor="#722ed1"
                style={{ marginTop: '12px' }}
                strokeWidth={6}
              />
            </div>
          </Card>
        </Col>
      </Row>

      {/* Performance Metrics and Charts */}
      <Row gutter={[32, 32]} style={{ marginBottom: '40px' }}>
        <Col xs={24} lg={16}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <LineChartOutlined style={{ color: '#1890ff' }} />
                <span>Training Progress</span>
              </div>
            }
            className="dashboard-card"
            style={{ height: '400px' }}
          >
            <ReactECharts
              option={getTrainingProgressChart()}
              style={{ height: '320px' }}
              theme="dark"
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <BarChartOutlined style={{ color: '#52c41a' }} />
                <span>Key Metrics</span>
              </div>
            }
            className="dashboard-card"
            style={{ height: '400px' }}
          >
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                <div style={{ fontSize: '28px', fontWeight: 700, color: '#1890ff', marginBottom: '8px' }}>
                  {systemStats.totalRewards.toLocaleString()}
                </div>
                <div style={{ fontSize: '14px', color: 'rgba(255,255,255,0.7)' }}>
                  Total Rewards Earned
                </div>
              </div>
              <Divider style={{ borderColor: 'rgba(255,255,255,0.1)' }} />
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                <div style={{ fontSize: '28px', fontWeight: 700, color: '#52c41a', marginBottom: '8px' }}>
                  {systemStats.avgSuccessRate}%
                </div>
                <div style={{ fontSize: '14px', color: 'rgba(255,255,255,0.7)' }}>
                  Average Success Rate
                </div>
              </div>
              <Divider style={{ borderColor: 'rgba(255,255,255,0.1)' }} />
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                <div style={{ fontSize: '28px', fontWeight: 700, color: '#faad14', marginBottom: '8px' }}>
                  {systemStats.totalEpisodes.toLocaleString()}
                </div>
                <div style={{ fontSize: '14px', color: 'rgba(255,255,255,0.7)' }}>
                  Total Episodes
                </div>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      <Row gutter={[32, 32]}>
        {/* System Metrics Chart */}
        <Col xs={24} lg={16}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <PieChartOutlined style={{ color: '#722ed1' }} />
                <span>System Performance</span>
              </div>
            }
            className="dashboard-card"
            style={{ height: '400px' }}
          >
            <ReactECharts
              option={getSystemMetricsChart()}
              style={{ height: '320px' }}
              theme="dark"
            />
          </Card>
        </Col>

        {/* Service Status and Recent Activities */}
        <Col xs={24} lg={8}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            {/* Service Status */}
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <MonitorOutlined style={{ color: '#52c41a' }} />
                  <span>Service Status</span>
                </div>
              }
              className="dashboard-card"
              style={{ height: '180px' }}
            >
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                {serviceStatuses.slice(0, 3).map((service, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                      {getStatusIcon(service.status)}
                      <Text style={{ color: '#ffffff', fontSize: '14px' }}>{service.name}</Text>
                    </div>
                    <Tag color={service.status === 'online' ? 'green' : 'orange'}>
                      {service.uptime}
                    </Tag>
                  </div>
                ))}
              </Space>
            </Card>

            {/* Recent Activities */}
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <ClockCircleOutlined style={{ color: '#1890ff' }} />
                  <span>Recent Activities</span>
                </div>
              }
              className="dashboard-card"
              style={{ height: '200px' }}
            >
              <Timeline
                size="small"
                items={recentActivities.slice(0, 3).map((activity, index) => ({
                  dot: activity.icon,
                  children: (
                    <div>
                      <Text style={{ fontSize: '13px', color: '#ffffff', display: 'block' }}>
                        {activity.title}
                      </Text>
                      <Text style={{ fontSize: '11px', color: 'rgba(255,255,255,0.5)' }}>
                        {activity.timestamp}
                      </Text>
                    </div>
                  ),
                }))}
              />
            </Card>
          </Space>
        </Col>
      </Row>
    </div>
  );
};

export default DashboardView;