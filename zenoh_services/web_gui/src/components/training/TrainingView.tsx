import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Form, Input, Select, Button, Progress, Typography, Space, Table, Tag, Divider, Statistic, Switch, InputNumber } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  SettingOutlined,
  Bar<PERSON><PERSON>Outlined,
  ReloadOutlined,
  SaveOutlined,
  ExperimentOutlined,
} from '@ant-design/icons';
import { useZenohStore } from '../../stores/zenoh-store';
import { TrainingConfig, TrainingState, ZENOH_TOPICS } from '../../types/zenoh-types';
import TrainingChart from './TrainingChart';
import TrainingComparison from './TrainingComparison';

const { Title, Text } = Typography;
const { Option } = Select;

interface TrainingViewProps {}

interface TrainingJob {
  key: string;
  id: string;
  taskName: string;
  status: 'running' | 'completed' | 'paused' | 'failed';
  progress: number;
  startTime: string;
  duration: string;
  reward: number;
}

const TrainingView: React.FC<TrainingViewProps> = () => {
  const [form] = Form.useForm();
  const { client, trainingStatus, trainingMetrics, sendTrainingCommand } = useZenohStore();
  const [configExpanded, setConfigExpanded] = useState(false);

  // Local state for UI
  const [localProgress, setLocalProgress] = useState(0);

  useEffect(() => {
    // Update local progress based on training status
    if (trainingStatus) {
      setLocalProgress(trainingStatus.progress_percentage);
    }
  }, [trainingStatus]);

  const isTraining = trainingStatus?.state === TrainingState.TRAINING;
  const isPaused = trainingStatus?.state === TrainingState.PAUSED;
  const canStart = !isTraining && !isPaused;

  // Mock training jobs data
  const trainingJobs: TrainingJob[] = [
    {
      key: '1',
      id: 'train_001',
      taskName: 'anymal_c_rough_terrain',
      status: 'running',
      progress: 75,
      startTime: '2024-01-15 14:30:00',
      duration: '2h 15m',
      reward: 128.5,
    },
    {
      key: '2',
      id: 'train_002',
      taskName: 'anymal_c_flat',
      status: 'completed',
      progress: 100,
      startTime: '2024-01-15 10:00:00',
      duration: '3h 45m',
      reward: 195.2,
    },
    {
      key: '3',
      id: 'train_003',
      taskName: 'anymal_c_stairs',
      status: 'paused',
      progress: 45,
      startTime: '2024-01-15 16:20:00',
      duration: '1h 12m',
      reward: 87.3,
    },
  ];

  const columns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const color = {
          running: 'blue',
          completed: 'green',
          paused: 'orange',
          failed: 'red',
        }[status] || 'default';
        const statusText = {
          running: '运行中',
          completed: '已完成',
          paused: '已暂停',
          failed: '失败',
        }[status] || status;
        return <Tag color={color}>{statusText}</Tag>;
      },
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => (
        <Progress percent={progress} size="small" />
      ),
    },
    {
      title: '持续时间',
      dataIndex: 'duration',
      key: 'duration',
    },
    {
      title: '最佳奖励',
      dataIndex: 'reward',
      key: 'reward',
      render: (reward: number) => reward.toFixed(1),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: TrainingJob) => (
        <Space>
          {record.status === 'running' ? (
            <Button 
              type="text" 
              icon={<PauseCircleOutlined />}
              size="small"
            />
          ) : (
            <Button 
              type="text" 
              icon={<PlayCircleOutlined />}
              size="small"
            />
          )}
          <Button 
            type="text" 
            icon={<StopOutlined />}
            size="small"
            danger
          />
        </Space>
      ),
    },
  ];

  const handleStartTraining = async (values: any) => {
    if (!client) return;

    const config: TrainingConfig = {
      task_name: values.taskName,
      num_envs: values.numEnvs,
      max_iterations: values.maxIterations,
      learning_rate: values.learningRate,
      batch_size: values.batchSize || 32,
      experiment_name: values.experimentName || 'default_experiment',
      run_name: values.runName || `run_${Date.now()}`,
      resume: values.resume || false,
      checkpoint: values.checkpoint || 0,
    };

    console.log('Starting training with config:', config);
    sendTrainingCommand('start', { config });
  };

  const handlePauseTraining = () => {
    if (!client) return;
    sendTrainingCommand('pause', {});
  };

  const handleResumeTraining = () => {
    if (!client) return;
    sendTrainingCommand('resume', {});
  };

  const handleStopTraining = () => {
    if (!client) return;
    sendTrainingCommand('stop', {});
  };

  const handleSaveCheckpoint = () => {
    if (!client) return;
    sendTrainingCommand('save_checkpoint', {});
  };

  return (
    <div>
      <Title level={2} style={{ marginBottom: '24px', color: 'white' }}>
        Training Management
      </Title>

      {/* Training Status Overview */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={6}>
          <Card>
            <Statistic
              title="Current Status"
              value={trainingStatus?.state || TrainingState.IDLE}
              valueStyle={{
                color: isTraining ? '#3f8600' : isPaused ? '#faad14' : '#8c8c8c'
              }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={6}>
          <Card>
            <Statistic
              title="Progress"
              value={localProgress}
              precision={1}
              suffix="%"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={6}>
          <Card>
            <Statistic
              title="Current Iteration"
              value={trainingStatus?.current_iteration || 0}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={6}>
          <Card>
            <Statistic
              title="Best Reward"
              value={trainingMetrics?.mean_reward || 0}
              precision={2}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <SettingOutlined />
                <span>Training Configuration</span>
              </Space>
            }
            extra={
              <Button
                type="text"
                icon={configExpanded ? <ReloadOutlined /> : <ExperimentOutlined />}
                onClick={() => setConfigExpanded(!configExpanded)}
              >
                {configExpanded ? 'Simple' : 'Advanced'}
              </Button>
            }
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleStartTraining}
              initialValues={{
                taskName: 'anymal_c_rough_terrain',
                numEnvs: 4096,
                maxIterations: 1500,
                learningRate: 0.0003,
                batchSize: 32,
                experimentName: 'default_experiment',
                runName: `run_${new Date().toISOString().slice(0, 10)}`,
                resume: false,
                checkpoint: 0,
              }}
            >
              {/* Basic Configuration */}
              <Form.Item
                label="Task Name"
                name="taskName"
                rules={[{ required: true, message: 'Please select a task!' }]}
              >
                <Select>
                  <Option value="anymal_c_rough_terrain">Anymal-C Rough Terrain</Option>
                  <Option value="anymal_c_flat">Anymal-C Flat</Option>
                  <Option value="anymal_c_stairs">Anymal-C Stairs</Option>
                  <Option value="anymal_c_extreme">Anymal-C Extreme Terrain</Option>
                  <Option value="zqsa01_rough_terrain">ZQSA01 Rough Terrain</Option>
                </Select>
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="Number of Environments"
                    name="numEnvs"
                    rules={[{ required: true, message: 'Please input number of environments!' }]}
                  >
                    <InputNumber min={1} max={16384} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Max Iterations"
                    name="maxIterations"
                    rules={[{ required: true, message: 'Please input max iterations!' }]}
                  >
                    <InputNumber min={1} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              {/* Advanced Configuration - shown when expanded */}
              {configExpanded && (
                <>
                  <Divider>Advanced Settings</Divider>
                  
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="Learning Rate"
                        name="learningRate"
                        rules={[{ required: true, message: 'Please input learning rate!' }]}
                      >
                        <InputNumber
                          min={0.00001}
                          max={0.01}
                          step={0.00001}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="Batch Size"
                        name="batchSize"
                      >
                        <InputNumber min={1} max={1024} style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="Experiment Name"
                        name="experimentName"
                      >
                        <Input placeholder="Enter experiment name" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="Run Name"
                        name="runName"
                      >
                        <Input placeholder="Enter run name" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="Resume Training"
                        name="resume"
                        valuePropName="checked"
                      >
                        <Switch />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="Checkpoint"
                        name="checkpoint"
                      >
                        <InputNumber min={0} style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                  </Row>
                </>
              )}

              <Form.Item>
                <Space>
                  <Button 
                    type="primary" 
                    htmlType="submit"
                    icon={<PlayCircleOutlined />}
                    loading={isTraining}
                    disabled={!canStart}
                  >
                    Start Training
                  </Button>
                  
                  {isPaused ? (
                    <Button 
                      type="default"
                      icon={<PlayCircleOutlined />}
                      onClick={handleResumeTraining}
                    >
                      Resume
                    </Button>
                  ) : (
                    <Button 
                      icon={<PauseCircleOutlined />}
                      onClick={handlePauseTraining}
                      disabled={!isTraining}
                    >
                      Pause
                    </Button>
                  )}
                  
                  <Button 
                    danger
                    icon={<StopOutlined />}
                    onClick={handleStopTraining}
                    disabled={!isTraining && !isPaused}
                  >
                    Stop
                  </Button>
                  
                  <Button 
                    icon={<SaveOutlined />}
                    onClick={handleSaveCheckpoint}
                    disabled={!isTraining}
                  >
                    Save Checkpoint
                  </Button>
                </Space>
              </Form.Item>
            </Form>

            {(isTraining || isPaused) && (
              <div style={{ marginTop: '16px' }}>
                <Text strong>Training Progress:</Text>
                <Progress 
                  percent={Math.round(localProgress)} 
                  status={isTraining ? 'active' : isPaused ? 'normal' : 'success'}
                  style={{ marginTop: '8px' }}
                />
                {trainingStatus?.status_message && (
                  <Text type="secondary" style={{ display: 'block', marginTop: '8px' }}>
                    {trainingStatus.status_message}
                  </Text>
                )}
              </div>
            )}
          </Card>
        </Col>

        {/* Training Metrics Chart */}
        <Col xs={24} lg={12}>
          <Card title={
            <Space>
              <BarChartOutlined />
              <span>Training Metrics</span>
            </Space>
          }>
            <TrainingChart />
          </Card>
        </Col>
      </Row>

      {/* Training Jobs Table */}
      <Row>
        <Col span={24}>
          <Card title="Training Jobs">
            <Table 
              columns={columns} 
              dataSource={trainingJobs}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>

      {/* Training Comparison */}
      <Row>
        <Col span={24}>
          <TrainingComparison />
        </Col>
      </Row>
    </div>
  );
};

export default TrainingView;