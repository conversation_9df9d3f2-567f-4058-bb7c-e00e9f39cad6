import React, { useState } from 'react';
import { Row, Col, Card, Form, Input, Select, Button, Slider, Typography, Space, Switch, Divider, Tabs } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  SettingOutlined,
  ExperimentOutlined,
  EnvironmentOutlined,
  RobotOutlined,
  BarChartOutlined,
} from '@ant-design/icons';
import RobotControlPanel from './RobotControlPanel';
import RobotStateDisplay from './RobotStateDisplay';
import SimulationConfiguration from './SimulationConfiguration';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

interface SimulationViewProps {}

const SimulationView: React.FC<SimulationViewProps> = () => {
  const [form] = Form.useForm();
  const [isSimulating, setIsSimulating] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleStartSimulation = async (values: any) => {
    setIsSimulating(true);
    console.log('Starting simulation with config:', values);
  };

  const handleStopSimulation = () => {
    setIsSimulating(false);
  };

  return (
    <div>
      <Title level={2} style={{ marginBottom: '24px', color: 'white' }}>
        Simulation & Robot Control
      </Title>

      <Tabs defaultActiveKey="control" type="card">
        {/* Robot Control Tab */}
        <TabPane
          tab={
            <span>
              <RobotOutlined />
              Robot Control
            </span>
          }
          key="control"
        >
          <RobotControlPanel />
        </TabPane>

        {/* Robot State Tab */}
        <TabPane
          tab={
            <span>
              <BarChartOutlined />
              Robot State
            </span>
          }
          key="state"
        >
          <RobotStateDisplay showCharts={true} />
        </TabPane>

        {/* Advanced Configuration Tab */}
        <TabPane
          tab={
            <span>
              <SettingOutlined />
              Advanced Config
            </span>
          }
          key="configuration"
        >
          <SimulationConfiguration />
        </TabPane>

        {/* Environment Configuration Tab */}
        <TabPane
          tab={
            <span>
              <EnvironmentOutlined />
              Environment
            </span>
          }
          key="environment"
        >
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            {/* Environment Configuration */}
            <Col xs={24} lg={16}>
              <Card title={
                <Space>
                  <EnvironmentOutlined />
                  <span>Environment Configuration</span>
                </Space>
              }>
                <Form
                  form={form}
                  layout="vertical"
                  initialValues={{
                    terrainType: 'rough',
                    terrainLength: 8.0,
                    terrainWidth: 8.0,
                    gravity: -9.81,
                    timeStep: 0.005,
                  }}
                >
                  <Form.Item label="Terrain Type" name="terrainType">
                    <Select>
                      <Option value="flat">Flat Ground</Option>
                      <Option value="rough">Rough Terrain</Option>
                      <Option value="stairs">Stairs</Option>
                      <Option value="slope">Slope</Option>
                      <Option value="random">Random Obstacles</Option>
                    </Select>
                  </Form.Item>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="Terrain Length (m)" name="terrainLength">
                        <Input type="number" step="0.1" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="Terrain Width (m)" name="terrainWidth">
                        <Input type="number" step="0.1" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <div>
                    <Text strong>Advanced Settings</Text>
                    <Switch 
                      checked={showAdvanced}
                      onChange={setShowAdvanced}
                      style={{ marginLeft: '16px' }}
                    />
                  </div>

                  {showAdvanced && (
                    <div style={{ marginTop: '16px' }}>
                      <Form.Item label="Gravity (m/s²)" name="gravity">
                        <Slider
                          min={-15}
                          max={0}
                          step={0.1}
                          marks={{
                            '-15': '-15',
                            '-9.81': 'Earth',
                            '-3.71': 'Mars',
                            '-1.62': 'Moon',
                            '0': '0'
                          }}
                        />
                      </Form.Item>

                      <Form.Item label="Time Step (s)" name="timeStep">
                        <Slider
                          min={0.001}
                          max={0.02}
                          step={0.001}
                          marks={{
                            0.001: '0.001',
                            0.005: '0.005',
                            0.01: '0.01',
                            0.02: '0.02'
                          }}
                        />
                      </Form.Item>

                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item label="Wind Speed (m/s)" name="windSpeed">
                            <Input type="number" step="0.1" defaultValue={0} />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item label="Friction Coefficient" name="friction">
                            <Input type="number" step="0.01" defaultValue={1.0} />
                          </Form.Item>
                        </Col>
                      </Row>
                    </div>
                  )}

                  <div style={{ marginTop: '24px' }}>
                    <Space>
                      <Button 
                        type="primary" 
                        icon={<PlayCircleOutlined />}
                        onClick={() => handleStartSimulation(form.getFieldsValue())}
                        loading={isSimulating}
                        disabled={isSimulating}
                      >
                        Apply Configuration
                      </Button>
                      <Button 
                        icon={<SettingOutlined />}
                        onClick={() => form.resetFields()}
                      >
                        Reset to Defaults
                      </Button>
                    </Space>
                  </div>
                </Form>
              </Card>
            </Col>

            {/* Simulation Status */}
            <Col xs={24} lg={8}>
              <Card title="Simulation Status">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text strong>Status:</Text>
                    <Text style={{ marginLeft: '8px', color: isSimulating ? '#52c41a' : '#8c8c8c' }}>
                      {isSimulating ? 'Running' : 'Stopped'}
                    </Text>
                  </div>
                  <div>
                    <Text strong>Physics Engine:</Text>
                    <Text style={{ marginLeft: '8px' }}>Isaac Gym</Text>
                  </div>
                  <div>
                    <Text strong>Renderer:</Text>
                    <Text style={{ marginLeft: '8px' }}>OpenGL</Text>
                  </div>
                  
                  <Divider />
                  
                  <Space>
                    <Button 
                      type="primary" 
                      icon={<PlayCircleOutlined />}
                      onClick={() => handleStartSimulation(form.getFieldsValue())}
                      loading={isSimulating}
                      disabled={isSimulating}
                    >
                      Start Simulation
                    </Button>
                    <Button 
                      danger
                      icon={<StopOutlined />}
                      onClick={handleStopSimulation}
                      disabled={!isSimulating}
                    >
                      Stop
                    </Button>
                  </Space>
                </Space>
              </Card>

              {/* Quick Metrics */}
              <Card title="Performance Metrics" style={{ marginTop: '16px' }}>
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#52c41a' }}>
                        {isSimulating ? '1247' : '0'}
                      </div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>Steps</Text>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#1890ff' }}>
                        {isSimulating ? '62.4s' : '0.0s'}
                      </div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>Sim Time</Text>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#722ed1' }}>
                        {isSimulating ? '156.8' : '0.0'}
                      </div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>Reward</Text>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#fa8c16' }}>
                        {isSimulating ? '98%' : '0%'}
                      </div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>Success</Text>
                    </div>
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default SimulationView;