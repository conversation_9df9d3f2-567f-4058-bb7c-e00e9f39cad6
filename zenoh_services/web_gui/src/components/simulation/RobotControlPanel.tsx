import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Form, Button, Slider, Typography, Space, Switch, Input, Select, InputNumber, Tabs, Alert } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ControlOutlined,
  RobotOutlined,
  SettingOutlined,
  ReloadOutlined,
  DashboardOutlined,
} from '@ant-design/icons';
import { useZenohStore } from '../../stores/zenoh-store';
import { RobotCommand, ZENOH_TOPICS, MessageType } from '../../types/zenoh-types';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

interface RobotControlPanelProps {}

interface JointState {
  position: number;
  velocity: number;
  torque: number;
}

const RobotControlPanel: React.FC<RobotControlPanelProps> = () => {
  const { client, robotState, playStatus, sendCommand } = useZenohStore();
  const [controlForm] = Form.useForm();
  const [jointForm] = Form.useForm();
  
  const [controlMode, setControlMode] = useState<'velocity' | 'position' | 'torque'>('velocity');
  const [isControlling, setIsControlling] = useState(false);
  const [emergencyStop, setEmergencyStop] = useState(false);
  
  // Base control states
  const [linearVelocity, setLinearVelocity] = useState([0, 0, 0]);
  const [angularVelocity, setAngularVelocity] = useState([0, 0, 0]);
  
  // Joint control states (12 joints for quadruped)
  const [jointTargets, setJointTargets] = useState<JointState[]>(
    Array.from({ length: 12 }, () => ({ position: 0, velocity: 0, torque: 0 }))
  );

  // Joint names for better UX
  const jointNames = [
    'FL_hip_joint', 'FL_thigh_joint', 'FL_calf_joint',
    'FR_hip_joint', 'FR_thigh_joint', 'FR_calf_joint',
    'RL_hip_joint', 'RL_thigh_joint', 'RL_calf_joint',
    'RR_hip_joint', 'RR_thigh_joint', 'RR_calf_joint'
  ];

  const legNames = ['Front Left', 'Front Right', 'Rear Left', 'Rear Right'];

  useEffect(() => {
    // Update control state based on play status
    setIsControlling(playStatus?.state === 'running');
  }, [playStatus]);

  const handleEmergencyStop = () => {
    setEmergencyStop(true);
    setIsControlling(false);
    
    // Send emergency stop command
    if (client) {
      sendCommand(ZENOH_TOPICS.ROBOT_COMMAND, 'emergency_stop', {});
    }
    
    // Reset all controls
    setLinearVelocity([0, 0, 0]);
    setAngularVelocity([0, 0, 0]);
    
    setTimeout(() => setEmergencyStop(false), 2000);
  };

  const handleStartControl = () => {
    setIsControlling(true);
    if (client) {
      sendCommand(ZENOH_TOPICS.PLAY_COMMAND, 'start', {
        control_mode: 'manual',
        model_path: 'latest'
      });
    }
  };

  const handleStopControl = () => {
    setIsControlling(false);
    if (client) {
      sendCommand(ZENOH_TOPICS.PLAY_COMMAND, 'stop', {});
    }
  };

  const sendRobotCommand = () => {
    if (!client || !isControlling) return;

    const command: RobotCommand = {
      linear_velocity: linearVelocity as [number, number, number],
      angular_velocity: angularVelocity as [number, number, number],
      timestamp: Date.now() / 1000,
      command_id: `cmd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };

    client.publish(ZENOH_TOPICS.ROBOT_COMMAND, {
      header: {
        message_id: command.command_id,
        timestamp: command.timestamp,
        message_type: MessageType.COMMAND,
        priority: 1,
        source_service: 'web_gui',
        destination_service: 'play_service',
        correlation_id: '',
        ttl: 1
      },
      payload: command,
      metadata: { control_mode: controlMode }
    });
  };

  const sendJointCommands = () => {
    if (!client || !isControlling) return;

    const jointCommands = jointTargets.map((joint, index) => ({
      joint_index: index,
      joint_name: jointNames[index],
      target_position: joint.position,
      target_velocity: joint.velocity,
      target_torque: joint.torque,
      control_mode: controlMode
    }));

    sendCommand(ZENOH_TOPICS.ROBOT_COMMAND, 'joint_control', {
      joints: jointCommands,
      timestamp: Date.now() / 1000
    });
  };

  // Auto-send commands when values change
  useEffect(() => {
    if (isControlling && !emergencyStop) {
      const interval = setInterval(sendRobotCommand, 100); // 10Hz
      return () => clearInterval(interval);
    }
  }, [linearVelocity, angularVelocity, isControlling, emergencyStop]);

  const handleJointChange = (jointIndex: number, field: keyof JointState, value: number) => {
    setJointTargets(prev => {
      const newTargets = [...prev];
      newTargets[jointIndex] = { ...newTargets[jointIndex], [field]: value };
      return newTargets;
    });
  };

  const resetAllJoints = () => {
    setJointTargets(Array.from({ length: 12 }, () => ({ position: 0, velocity: 0, torque: 0 })));
  };

  return (
    <div>
      <Title level={2} style={{ marginBottom: '24px', color: 'white' }}>
        Robot Control Panel
      </Title>

      {/* Emergency Stop Alert */}
      {emergencyStop && (
        <Alert
          message="Emergency Stop Activated"
          description="All robot movements have been stopped for safety."
          type="error"
          showIcon
          style={{ marginBottom: '16px' }}
        />
      )}

      {/* Control Status and Master Controls */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={8}>
          <Card>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ textAlign: 'center' }}>
                <RobotOutlined 
                  style={{ 
                    fontSize: '32px', 
                    color: isControlling ? '#52c41a' : '#8c8c8c',
                    marginBottom: '8px'
                  }} 
                />
                <div>
                  <Text strong style={{ color: 'white', fontSize: '16px' }}>
                    Status: {isControlling ? 'Active Control' : 'Standby'}
                  </Text>
                </div>
              </div>
              
              <Space style={{ width: '100%', justifyContent: 'center' }}>
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={handleStartControl}
                  disabled={isControlling}
                >
                  Start Control
                </Button>
                <Button
                  icon={<PauseCircleOutlined />}
                  onClick={handleStopControl}
                  disabled={!isControlling}
                >
                  Stop Control
                </Button>
                <Button
                  danger
                  icon={<StopOutlined />}
                  onClick={handleEmergencyStop}
                  style={{ marginLeft: '8px' }}
                >
                  E-STOP
                </Button>
              </Space>
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="Control Mode">
            <Select
              value={controlMode}
              onChange={setControlMode}
              style={{ width: '100%' }}
              disabled={isControlling}
            >
              <Option value="velocity">Velocity Control</Option>
              <Option value="position">Position Control</Option>
              <Option value="torque">Torque Control</Option>
            </Select>
            <Text 
              type="secondary" 
              style={{ display: 'block', marginTop: '8px', fontSize: '12px' }}
            >
              {controlMode === 'velocity' && 'Control robot through velocity commands'}
              {controlMode === 'position' && 'Control robot through position targets'}
              {controlMode === 'torque' && 'Control robot through torque commands'}
            </Text>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="Robot Info">
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <div>
                <Text type="secondary">Model:</Text>
                <Text style={{ marginLeft: '8px' }}>ZQSA01 Quadruped</Text>
              </div>
              <div>
                <Text type="secondary">DOF:</Text>
                <Text style={{ marginLeft: '8px' }}>12 Joints</Text>
              </div>
              <div>
                <Text type="secondary">Update Rate:</Text>
                <Text style={{ marginLeft: '8px' }}>10 Hz</Text>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* Control Tabs */}
      <Row>
        <Col span={24}>
          <Card>
            <Tabs defaultActiveKey="base" type="card">
              {/* Base Control Tab */}
              <TabPane
                tab={
                  <span>
                    <ControlOutlined />
                    Base Control
                  </span>
                }
                key="base"
              >
                <Row gutter={[24, 16]}>
                  <Col xs={24} lg={12}>
                    <Title level={4} style={{ color: 'white' }}>Linear Velocity (m/s)</Title>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text>Forward/Backward (X):</Text>
                        <Slider
                          min={-2.0}
                          max={2.0}
                          step={0.1}
                          value={linearVelocity[0]}
                          onChange={(value) => setLinearVelocity([value, linearVelocity[1], linearVelocity[2]])}
                          marks={{
                            '-2': '-2.0',
                            '-1': '-1.0',
                            '0': '0.0',
                            '1': '1.0',
                            '2': '2.0'
                          }}
                          disabled={!isControlling}
                        />
                        <InputNumber
                          value={linearVelocity[0]}
                          min={-2.0}
                          max={2.0}
                          step={0.1}
                          onChange={(value) => setLinearVelocity([value || 0, linearVelocity[1], linearVelocity[2]])}
                          disabled={!isControlling}
                          size="small"
                          style={{ width: '80px', marginLeft: '16px' }}
                        />
                      </div>

                      <div>
                        <Text>Left/Right (Y):</Text>
                        <Slider
                          min={-1.0}
                          max={1.0}
                          step={0.1}
                          value={linearVelocity[1]}
                          onChange={(value) => setLinearVelocity([linearVelocity[0], value, linearVelocity[2]])}
                          marks={{
                            '-1': '-1.0',
                            '-0.5': '-0.5',
                            '0': '0.0',
                            '0.5': '0.5',
                            '1': '1.0'
                          }}
                          disabled={!isControlling}
                        />
                        <InputNumber
                          value={linearVelocity[1]}
                          min={-1.0}
                          max={1.0}
                          step={0.1}
                          onChange={(value) => setLinearVelocity([linearVelocity[0], value || 0, linearVelocity[2]])}
                          disabled={!isControlling}
                          size="small"
                          style={{ width: '80px', marginLeft: '16px' }}
                        />
                      </div>

                      <div>
                        <Text>Up/Down (Z):</Text>
                        <Slider
                          min={-0.5}
                          max={0.5}
                          step={0.05}
                          value={linearVelocity[2]}
                          onChange={(value) => setLinearVelocity([linearVelocity[0], linearVelocity[1], value])}
                          marks={{
                            '-0.5': '-0.5',
                            '-0.25': '-0.25',
                            '0': '0.0',
                            '0.25': '0.25',
                            '0.5': '0.5'
                          }}
                          disabled={!isControlling}
                        />
                        <InputNumber
                          value={linearVelocity[2]}
                          min={-0.5}
                          max={0.5}
                          step={0.05}
                          onChange={(value) => setLinearVelocity([linearVelocity[0], linearVelocity[1], value || 0])}
                          disabled={!isControlling}
                          size="small"
                          style={{ width: '80px', marginLeft: '16px' }}
                        />
                      </div>
                    </Space>
                  </Col>

                  <Col xs={24} lg={12}>
                    <Title level={4} style={{ color: 'white' }}>Angular Velocity (rad/s)</Title>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text>Roll (X):</Text>
                        <Slider
                          min={-1.0}
                          max={1.0}
                          step={0.1}
                          value={angularVelocity[0]}
                          onChange={(value) => setAngularVelocity([value, angularVelocity[1], angularVelocity[2]])}
                          marks={{
                            '-1': '-1.0',
                            '-0.5': '-0.5',
                            '0': '0.0',
                            '0.5': '0.5',
                            '1': '1.0'
                          }}
                          disabled={!isControlling}
                        />
                        <InputNumber
                          value={angularVelocity[0]}
                          min={-1.0}
                          max={1.0}
                          step={0.1}
                          onChange={(value) => setAngularVelocity([value || 0, angularVelocity[1], angularVelocity[2]])}
                          disabled={!isControlling}
                          size="small"
                          style={{ width: '80px', marginLeft: '16px' }}
                        />
                      </div>

                      <div>
                        <Text>Pitch (Y):</Text>
                        <Slider
                          min={-1.0}
                          max={1.0}
                          step={0.1}
                          value={angularVelocity[1]}
                          onChange={(value) => setAngularVelocity([angularVelocity[0], value, angularVelocity[2]])}
                          marks={{
                            '-1': '-1.0',
                            '-0.5': '-0.5',
                            '0': '0.0',
                            '0.5': '0.5',
                            '1': '1.0'
                          }}
                          disabled={!isControlling}
                        />
                        <InputNumber
                          value={angularVelocity[1]}
                          min={-1.0}
                          max={1.0}
                          step={0.1}
                          onChange={(value) => setAngularVelocity([angularVelocity[0], value || 0, angularVelocity[2]])}
                          disabled={!isControlling}
                          size="small"
                          style={{ width: '80px', marginLeft: '16px' }}
                        />
                      </div>

                      <div>
                        <Text>Yaw (Z):</Text>
                        <Slider
                          min={-2.0}
                          max={2.0}
                          step={0.1}
                          value={angularVelocity[2]}
                          onChange={(value) => setAngularVelocity([angularVelocity[0], angularVelocity[1], value])}
                          marks={{
                            '-2': '-2.0',
                            '-1': '-1.0',
                            '0': '0.0',
                            '1': '1.0',
                            '2': '2.0'
                          }}
                          disabled={!isControlling}
                        />
                        <InputNumber
                          value={angularVelocity[2]}
                          min={-2.0}
                          max={2.0}
                          step={0.1}
                          onChange={(value) => setAngularVelocity([angularVelocity[0], angularVelocity[1], value || 0])}
                          disabled={!isControlling}
                          size="small"
                          style={{ width: '80px', marginLeft: '16px' }}
                        />
                      </div>
                    </Space>
                  </Col>
                </Row>

                <div style={{ textAlign: 'center', marginTop: '24px' }}>
                  <Space>
                    <Button
                      onClick={() => {
                        setLinearVelocity([0, 0, 0]);
                        setAngularVelocity([0, 0, 0]);
                      }}
                      disabled={!isControlling}
                    >
                      Reset All
                    </Button>
                    <Button
                      type="primary"
                      onClick={sendRobotCommand}
                      disabled={!isControlling}
                    >
                      Send Command
                    </Button>
                  </Space>
                </div>
              </TabPane>

              {/* Joint Control Tab */}
              <TabPane
                tab={
                  <span>
                    <SettingOutlined />
                    Joint Control
                  </span>
                }
                key="joints"
              >
                <div style={{ marginBottom: '16px' }}>
                  <Space>
                    <Button onClick={resetAllJoints} disabled={!isControlling}>
                      Reset All Joints
                    </Button>
                    <Button type="primary" onClick={sendJointCommands} disabled={!isControlling}>
                      Send Joint Commands
                    </Button>
                  </Space>
                </div>

                <Row gutter={[16, 16]}>
                  {legNames.map((legName, legIndex) => (
                    <Col key={legName} xs={24} lg={12}>
                      <Card size="small" title={legName}>
                        {Array.from({ length: 3 }, (_, jointIndex) => {
                          const globalIndex = legIndex * 3 + jointIndex;
                          const jointName = jointNames[globalIndex];
                          const jointType = ['Hip', 'Thigh', 'Calf'][jointIndex];
                          
                          return (
                            <div key={globalIndex} style={{ marginBottom: '16px' }}>
                              <Text strong>{jointType} Joint:</Text>
                              <div style={{ marginTop: '8px' }}>
                                {controlMode === 'position' && (
                                  <div>
                                    <Text type="secondary">Position (rad):</Text>
                                    <Slider
                                      min={-3.14}
                                      max={3.14}
                                      step={0.1}
                                      value={jointTargets[globalIndex].position}
                                      onChange={(value) => handleJointChange(globalIndex, 'position', value)}
                                      disabled={!isControlling}
                                      style={{ margin: '8px 0' }}
                                    />
                                  </div>
                                )}
                                
                                {controlMode === 'velocity' && (
                                  <div>
                                    <Text type="secondary">Velocity (rad/s):</Text>
                                    <Slider
                                      min={-5.0}
                                      max={5.0}
                                      step={0.1}
                                      value={jointTargets[globalIndex].velocity}
                                      onChange={(value) => handleJointChange(globalIndex, 'velocity', value)}
                                      disabled={!isControlling}
                                      style={{ margin: '8px 0' }}
                                    />
                                  </div>
                                )}

                                {controlMode === 'torque' && (
                                  <div>
                                    <Text type="secondary">Torque (Nm):</Text>
                                    <Slider
                                      min={-50}
                                      max={50}
                                      step={1}
                                      value={jointTargets[globalIndex].torque}
                                      onChange={(value) => handleJointChange(globalIndex, 'torque', value)}
                                      disabled={!isControlling}
                                      style={{ margin: '8px 0' }}
                                    />
                                  </div>
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </Card>
                    </Col>
                  ))}
                </Row>
              </TabPane>
            </Tabs>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default RobotControlPanel;