import React, { useState } from 'react';
import { Layout, Menu, Typography, Badge, Space, Breadcrumb, Dropdown, Avatar, Button } from 'antd';
import {
  DashboardOutlined,
  ExperimentOutlined,
  ControlOutlined,
  SettingOutlined,
  DeploymentUnitOutlined,
  MonitorOutlined,
  RobotOutlined,
  UserOutlined,
  BellOutlined,
  SearchOutlined,
  FullscreenOutlined,
  HomeOutlined,
} from '@ant-design/icons';
import SystemStatusBar from './SystemStatusBar';
import DashboardView from '../dashboard/DashboardView';
import TrainingView from '../training/TrainingView';
import SimulationView from '../simulation/SimulationView';
import DeploymentView from '../deployment/DeploymentView';
import MonitoringView from '../monitoring/MonitoringView';
import { useZenohInitialization } from '../../hooks/useZenohInitialization';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

interface MainLayoutProps {}

const MainLayout: React.FC<MainLayoutProps> = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [selectedKey, setSelectedKey] = useState('dashboard');

  // Initialize Zenoh client and connection
  const { isConnected } = useZenohInitialization({
    url: 'ws://localhost:8080/ws', // Default Zenoh WebSocket endpoint
    reconnectInterval: 5000,
    maxReconnectAttempts: 10,
    heartbeatInterval: 30000
  });

  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: 'Dashboard',
      description: 'System overview and status'
    },
    {
      key: 'training',
      icon: <ExperimentOutlined />,
      label: 'Training',
      description: 'Model training and experiments'
    },
    {
      key: 'simulation',
      icon: <RobotOutlined />,
      label: 'Simulation',
      description: 'Robot simulation environment'
    },
    {
      key: 'deployment',
      icon: <DeploymentUnitOutlined />,
      label: 'Deployment',
      description: 'Model deployment and management'
    },
    {
      key: 'monitoring',
      icon: <MonitorOutlined />,
      label: 'Monitoring',
      description: 'System monitoring and analytics'
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Settings',
      description: 'System configuration'
    },
  ];

  const userMenuItems = [
    {
      key: 'profile',
      label: 'User Profile',
    },
    {
      key: 'preferences',
      label: 'Preferences',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      label: 'Logout',
    },
  ];

  const getBreadcrumbItems = () => {
    const items = [
      {
        href: '#',
        title: <HomeOutlined />,
      },
    ];

    const currentItem = menuItems.find(item => item.key === selectedKey);
    if (currentItem) {
      items.push({
        title: currentItem.label,
      });
    }

    return items;
  };

  const renderContent = () => {
    switch (selectedKey) {
      case 'dashboard':
        return <DashboardView />;
      case 'training':
        return <TrainingView />;
      case 'simulation':
        return <SimulationView />;
      case 'deployment':
        return <DeploymentView />;
      case 'monitoring':
        return <MonitoringView />;
      case 'settings':
        return <div>Settings View (Coming Soon)</div>;
      default:
        return <DashboardView />;
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* Enhanced Sidebar */}
      <Sider
        theme="dark"
        collapsible
        collapsed={collapsed}
        onCollapse={setCollapsed}
        width={280}
        style={{
          background: 'linear-gradient(180deg, #001529 0%, #002140 100%)',
          boxShadow: '2px 0 8px rgba(0,0,0,0.15)'
        }}
      >
        {/* Logo Section */}
        <div style={{
          padding: collapsed ? '20px 12px' : '24px 20px',
          textAlign: 'center',
          borderBottom: '1px solid rgba(255,255,255,0.1)',
          background: 'rgba(0,0,0,0.2)'
        }}>
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <RobotOutlined
              style={{
                fontSize: collapsed ? '28px' : '40px',
                color: '#1890ff',
                transition: 'all 0.3s ease',
                filter: 'drop-shadow(0 2px 4px rgba(24,144,255,0.3))'
              }}
            />
            {!collapsed && (
              <>
                <Title level={3} style={{ margin: 0, color: 'white', fontWeight: 700 }}>
                  EngineAI
                </Title>
                <Text style={{ color: 'rgba(255,255,255,0.7)', fontSize: '12px' }}>
                  Legged Robot Platform
                </Text>
              </>
            )}
          </Space>
        </div>

        {/* Enhanced Menu */}
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[selectedKey]}
          style={{
            borderRight: 0,
            marginTop: '20px',
            background: 'transparent'
          }}
          onClick={({ key }) => setSelectedKey(key)}
        >
          {menuItems.map(item => (
            <Menu.Item
              key={item.key}
              icon={item.icon}
              style={{
                margin: '8px 16px',
                borderRadius: '8px',
                height: collapsed ? '48px' : '56px',
                lineHeight: collapsed ? '48px' : '56px',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                <span style={{ fontSize: '15px', fontWeight: 500 }}>{item.label}</span>
                {!collapsed && (
                  <span style={{
                    fontSize: '11px',
                    color: 'rgba(255,255,255,0.5)',
                    marginTop: '2px'
                  }}>
                    {item.description}
                  </span>
                )}
              </div>
            </Menu.Item>
          ))}
        </Menu>
      </Sider>

      <Layout>
        {/* Enhanced Header */}
        <Header style={{
          padding: '0 32px',
          background: 'linear-gradient(90deg, #001529 0%, #002140 100%)',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid rgba(255,255,255,0.1)',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          height: '72px'
        }}>
          {/* Left Section - Breadcrumb */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '24px' }}>
            <Breadcrumb
              items={getBreadcrumbItems()}
              style={{
                fontSize: '14px',
                color: 'rgba(255,255,255,0.7)'
              }}
            />
            <Title
              level={2}
              style={{
                margin: 0,
                color: 'white',
                fontWeight: 600,
                textTransform: 'capitalize'
              }}
            >
              {selectedKey}
            </Title>
          </div>

          {/* Right Section - Actions and Status */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <Button
              type="text"
              icon={<SearchOutlined />}
              style={{ color: 'rgba(255,255,255,0.7)' }}
            />
            <Button
              type="text"
              icon={<FullscreenOutlined />}
              style={{ color: 'rgba(255,255,255,0.7)' }}
            />
            <Badge count={3} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ color: 'rgba(255,255,255,0.7)' }}
              />
            </Badge>
            <SystemStatusBar />
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                cursor: 'pointer',
                padding: '8px 12px',
                borderRadius: '6px',
                transition: 'background 0.3s',
                ':hover': { background: 'rgba(255,255,255,0.1)' }
              }}>
                <Avatar size="small" icon={<UserOutlined />} />
                <Text style={{ color: 'white', fontSize: '14px' }}>Admin</Text>
              </div>
            </Dropdown>
          </div>
        </Header>

        {/* Enhanced Content Area */}
        <Content style={{
          padding: '32px 40px 40px 40px',
          background: '#0a0a0a',
          overflow: 'auto',
          minHeight: 'calc(100vh - 72px)',
          position: 'relative'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, rgba(24,144,255,0.03) 0%, rgba(24,144,255,0.01) 100%)',
            borderRadius: '12px',
            padding: '32px',
            minHeight: 'calc(100vh - 144px)',
            border: '1px solid rgba(255,255,255,0.05)'
          }}>
            {renderContent()}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;