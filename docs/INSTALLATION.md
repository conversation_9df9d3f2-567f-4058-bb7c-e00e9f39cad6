# EngineAI Legged Gym - 系统安装和配置文档

## 📋 目录

- [系统概述](#系统概述)
- [系统要求](#系统要求)
- [安装指南](#安装指南)
- [配置说明](#配置说明)
- [验证安装](#验证安装)
- [常见安装问题](#常见安装问题)

## 🎯 系统概述

EngineAI Legged Gym是一个基于强化学习的机器人仿真训练系统，支持：

- **强化学习训练**: 基于PPO算法的机器人控制策略训练
- **仿真环境**: 集成Isaac Gym的高性能物理仿真
- **分布式架构**: 基于Zenoh的微服务分布式系统
- **Web界面**: React + TypeScript的现代化用户界面
- **实时监控**: 完整的系统监控和性能分析

### 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web GUI       │    │   Zenoh Services│    │  Isaac Gym      │
│  (React + TS)   │<-->│  (Microservices)│<-->│  (Simulation)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                               │
                       ┌─────────────────┐
                       │   Training      │
                       │   (PPO + RSL)   │
                       └─────────────────┘
```

## 💻 系统要求

### 硬件要求

**最低要求**:
- CPU: Intel i5-8400 / AMD Ryzen 5 2600 或更高
- 内存: 16GB RAM
- GPU: NVIDIA GTX 1060 (6GB) 或更高
- 存储: 20GB 可用空间

**推荐配置**:
- CPU: Intel i7-10700K / AMD Ryzen 7 3700X 或更高
- 内存: 32GB RAM
- GPU: NVIDIA RTX 3070 (8GB) 或更高
- 存储: 50GB SSD 存储空间

### 软件要求

**操作系统**:
- Ubuntu 18.04 / 20.04 / 22.04 LTS (推荐)
- Windows 10/11 (部分支持)
- macOS 10.15+ (CPU模式)

**依赖软件**:
- Python 3.8-3.10
- CUDA 11.8+ (GPU训练)
- Node.js 18+ (Web界面)
- Git

## 🚀 安装指南

### 1. 环境准备

#### 1.1 安装Python和依赖

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3.9 python3.9-pip python3.9-dev python3.9-venv
sudo apt install git cmake build-essential

# 创建虚拟环境
python3.9 -m venv venv
source venv/bin/activate
```

#### 1.2 安装CUDA (GPU训练必需)

```bash
# 下载CUDA 11.8
wget https://developer.download.nvidia.com/compute/cuda/11.8.0/local_installers/cuda_11.8.0_520.61.05_linux.run
sudo sh cuda_11.8.0_520.61.05_linux.run

# 验证安装
nvcc --version
nvidia-smi
```

#### 1.3 安装Node.js (Web界面)

```bash
# 使用NodeSource仓库
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

### 2. 系统安装

#### 2.1 克隆项目

```bash
git clone https://github.com/your-org/engineai-legged-gym.git
cd engineai-legged-gym
```

#### 2.2 安装Python依赖

```bash
# 激活虚拟环境
source venv/bin/activate

# 安装核心依赖
pip install -r requirements.txt

# 安装Isaac Gym (需要NVIDIA账户下载)
# 1. 从NVIDIA官网下载 IsaacGym_Preview_4_Package.tar.gz
# 2. 解压并安装
tar -xf IsaacGym_Preview_4_Package.tar.gz
cd isaacgym/python && pip install -e .

# 安装项目包
pip install -e .
```

#### 2.3 安装Zenoh

```bash
# 安装Zenoh Python绑定
pip install zenoh

# 或从源码安装 (可选)
git clone https://github.com/eclipse-zenoh/zenoh-python.git
cd zenoh-python
pip install -r requirements-dev.txt
maturin develop --release
```

#### 2.4 安装Web界面依赖

```bash
cd zenoh_services/web_gui
npm install
```

### 3. 配置系统

#### 3.1 基础配置

创建配置文件 `config/system_config.yaml`:

```yaml
# 系统基础配置
system:
  name: "EngineAI Legged Gym"
  version: "1.0.0"
  environment: "production"  # development, staging, production

# Zenoh配置
zenoh:
  mode: "peer"  # peer, client, router
  listen:
    - "tcp/0.0.0.0:7447"
  connect:
    - "tcp/127.0.0.1:7447"
  config_path: "zenoh_services/config/"

# 训练配置
training:
  algorithm: "ppo"
  device: "cuda"  # cuda, cpu
  num_envs: 4096
  max_iterations: 1000
  save_interval: 100
  log_interval: 10

# 仿真配置
simulation:
  headless: false
  physics_engine: "physx"
  dt: 0.0083  # 120 Hz
  substeps: 1
  up_axis: "z"

# Web界面配置
web_gui:
  host: "0.0.0.0"
  port: 3000
  api_base_url: "http://localhost:8000"

# 监控配置
monitoring:
  enabled: true
  metrics_interval: 1.0  # 秒
  log_level: "info"  # debug, info, warning, error
  export_prometheus: true
```

#### 3.2 环境变量配置

创建 `.env` 文件:

```bash
# 基础路径
ENGINEAI_ROOT=/path/to/engineai-legged-gym
ISAACGYM_ROOT=/path/to/isaacgym

# CUDA配置
CUDA_VISIBLE_DEVICES=0
CUDA_LAUNCH_BLOCKING=1

# Zenoh配置
ZENOH_CONFIG_PATH=./zenoh_services/config/
ZENOH_LOG_LEVEL=info

# 训练配置
WANDB_API_KEY=your_wandb_key  # 可选，用于实验跟踪
ISAAC_GYM_ROOT_PATH=${ISAACGYM_ROOT}

# Web服务配置
REACT_APP_API_BASE_URL=http://localhost:8000
NODE_ENV=production
```

#### 3.3 机器人配置

编辑 `legged_gym/envs/zqsa01/zqsa01_config.py`:

```python
class ZQSA01Config(LeggedRobotConfig):
    class env:
        num_envs = 4096
        episode_length_s = 20  # 20秒一局
        
    class terrain:
        mesh_type = 'trimesh'
        curriculum = True
        max_init_terrain_level = 5
        
    class control:
        control_type = 'P'  # P, PD, torque
        stiffness = {'joint': 20.0}
        damping = {'joint': 0.5}
        
    class asset:
        file = '{LEGGED_GYM_ROOT_DIR}/resources/robots/zq_humanoid/urdf/zq_sa01.urdf'
        name = "zqsa01"
        foot_name = "foot"
        
    class rewards:
        tracking_sigma = 0.25
        soft_dof_pos_limit = 1.0
        base_height_target = 1.0
```

### 4. 服务配置

#### 4.1 Zenoh服务配置

创建 `zenoh_services/config/services_config.yaml`:

```yaml
services:
  training_service:
    topic_prefix: "/engineai/training"
    qos:
      reliability: "reliable"
      durability: "volatile"
    error_handling:
      retry_attempts: 3
      timeout_seconds: 30
      
  simulation_service:
    topic_prefix: "/engineai/simulation"
    physics_dt: 0.0083
    render_fps: 60
    
  deployment_service:
    topic_prefix: "/engineai/deployment"
    model_path: "./logs/trained_models/"
    onnx_export: true
    
  monitoring_service:
    topic_prefix: "/engineai/monitoring"
    metrics_collection_interval: 1.0
    alert_thresholds:
      cpu_usage: 80
      memory_usage: 85
      gpu_utilization: 90
```

#### 4.2 错误处理配置

确保 `zenoh_services/config/error_handling.yaml` 存在:

```yaml
error_handling:
  global:
    max_retry_attempts: 3
    base_retry_delay: 1.0
    max_retry_delay: 60.0
    exponential_backoff: true
    
  service_specific:
    training_service:
      critical_errors:
        - "CUDA_OUT_OF_MEMORY"
        - "MODEL_LOAD_FAILED"
      recovery_strategies:
        - "reduce_batch_size"
        - "fallback_to_cpu"
        
  notification:
    channels:
      - console
      - file
      - webhook  # 可选
```

## ✅ 验证安装

### 1. 基础环境验证

```bash
# 检查Python环境
python --version
pip list | grep torch
pip list | grep zenoh

# 检查CUDA (如果使用GPU)
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
python -c "import torch; print(f'CUDA devices: {torch.cuda.device_count()}')"

# 检查Isaac Gym
python -c "from isaacgym import gymapi; print('Isaac Gym imported successfully')"
```

### 2. 系统组件验证

```bash
# 验证Zenoh服务
cd zenoh_services
python -c "
import zenoh
session = zenoh.open()
print('Zenoh session created successfully')
session.close()
"

# 验证Web界面
cd zenoh_services/web_gui
npm run build
echo "Web GUI build successful"

# 验证训练环境
cd ../../
python -c "
from legged_gym.envs.zqsa01.zqsa01 import ZQSA01
from legged_gym.envs.zqsa01.zqsa01_config import ZQSA01Config
print('Training environment imported successfully')
"
```

### 3. 集成测试

```bash
# 运行快速集成测试
python -m pytest zenoh_services/tests/test_simple_integration.py -v

# 运行核心功能测试
python scripts/test_env.py

# 启动系统验证
python demo_services.py --verify-only
```

## 🔧 配置优化

### 1. 性能优化配置

#### GPU内存优化
```python
# 在训练脚本中添加
import torch
torch.backends.cudnn.benchmark = True
torch.backends.cudnn.deterministic = False

# 环境变量
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
```

#### CPU优化
```bash
# 设置OMP线程数
export OMP_NUM_THREADS=8
export MKL_NUM_THREADS=8
```

### 2. 网络配置

#### Zenoh网络优化
```yaml
# config/zenoh_network.yaml
zenoh:
  transport:
    unicast:
      lowlatency: true
    multicast:
      enabled: true
      interface: "auto"
  scouting:
    multicast:
      enabled: true
      address: "***********:7446"
```

### 3. 日志配置

#### 配置日志级别和输出
```yaml
# config/logging.yaml
logging:
  version: 1
  disable_existing_loggers: false
  
  formatters:
    detailed:
      format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    simple:
      format: '%(levelname)s - %(message)s'
      
  handlers:
    console:
      class: logging.StreamHandler
      level: INFO
      formatter: simple
      
    file:
      class: logging.handlers.RotatingFileHandler
      level: DEBUG
      formatter: detailed
      filename: logs/engineai.log
      maxBytes: 10485760  # 10MB
      backupCount: 5
      
  loggers:
    zenoh_services:
      level: INFO
      handlers: [console, file]
    legged_gym:
      level: INFO
      handlers: [console, file]
      
  root:
    level: INFO
    handlers: [console]
```

## 🐛 常见安装问题

### 1. CUDA相关问题

**问题**: CUDA out of memory
```bash
# 解决方案
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256
# 或在代码中
torch.cuda.empty_cache()
```

**问题**: CUDA版本不兼容
```bash
# 检查CUDA版本
nvcc --version
python -c "import torch; print(torch.version.cuda)"

# 重新安装匹配版本的PyTorch
pip uninstall torch torchvision
pip install torch==1.13.0+cu118 torchvision==0.14.0+cu118 --extra-index-url https://download.pytorch.org/whl/cu118
```

### 2. Isaac Gym问题

**问题**: 找不到Isaac Gym
```bash
# 确保正确设置环境变量
export ISAACGYM_ROOT_PATH=/path/to/isaacgym
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$ISAACGYM_ROOT_PATH/bindings/python/isaacgym/_bindings/linux-x86_64
```

**问题**: 缺少libpython3.x.so
```bash
# Ubuntu
sudo apt-get install python3.9-dev
# 或创建软链接
sudo ln -s /usr/lib/x86_64-linux-gnu/libpython3.9.so.1.0 /usr/lib/x86_64-linux-gnu/libpython3.9.so
```

### 3. Zenoh连接问题

**问题**: Zenoh会话创建失败
```bash
# 检查端口占用
netstat -tulpn | grep 7447
# 杀死占用进程
sudo fuser -k 7447/tcp
```

**问题**: 节点发现失败
```bash
# 检查多播设置
sudo sysctl -w net.ipv4.ip_forward=1
# 或使用TCP直连模式
zenoh_config['connect'] = ['tcp/127.0.0.1:7447']
```

### 4. Web界面问题

**问题**: npm install失败
```bash
# 清理缓存
npm cache clean --force
# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

**问题**: 构建失败
```bash
# 检查Node.js版本
node --version  # 应该是18+
# 增加内存限制
NODE_OPTIONS="--max-old-space-size=4096" npm run build
```

### 5. 权限问题

**问题**: CUDA权限不足
```bash
# 添加用户到docker组 (如果使用Docker)
sudo usermod -aG docker $USER
# 设置设备权限
sudo chmod 666 /dev/nvidia*
```

## 📦 Docker部署 (可选)

### 1. 使用Docker安装

```bash
# 构建Docker镜像
docker build -t engineai-legged-gym:latest .

# 运行容器
docker run --gpus all -p 3000:3000 -p 7447:7447 \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/config:/app/config \
  engineai-legged-gym:latest
```

### 2. Docker Compose

创建 `docker-compose.yml`:

```yaml
version: '3.8'
services:
  engineai:
    build: .
    ports:
      - "3000:3000"
      - "7447:7447"
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - ZENOH_LOG_LEVEL=info
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
```

运行:
```bash
docker-compose up -d
```

## 🔍 安装验证清单

- [ ] Python 3.8-3.10 已安装
- [ ] CUDA 11.8+ 已安装并工作
- [ ] Isaac Gym 导入成功
- [ ] PyTorch GPU 支持正常
- [ ] Zenoh 会话创建成功
- [ ] Web界面构建成功
- [ ] 训练环境加载正常
- [ ] 配置文件格式正确
- [ ] 日志目录可写
- [ ] 端口 3000, 7447 未被占用

## 📞 支持联系

如果遇到安装问题，请：

1. 查看[故障排除文档](TROUBLESHOOTING.md)
2. 检查[GitHub Issues](https://github.com/your-org/engineai-legged-gym/issues)
3. 提交新的Issue并包含:
   - 操作系统和版本
   - Python版本
   - CUDA版本
   - 错误日志
   - 安装步骤

---

**下一步**: 查看[用户操作指南](USER_GUIDE.md)了解如何使用系统。