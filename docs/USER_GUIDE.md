# EngineAI Legged Gym - 用户操作指南

## 📋 目录

- [快速入门](#快速入门)
- [Web界面使用](#web界面使用)
- [训练操作](#训练操作)
- [仿真控制](#仿真控制)
- [模型部署](#模型部署)
- [监控和调试](#监控和调试)
- [命令行工具](#命令行工具)

## 🚀 快速入门

### 启动系统

1. **启动Zenoh服务**
```bash
cd engineai-legged-gym
source venv/bin/activate

# 启动核心服务
python demo_services.py --start-all
```

2. **启动Web界面**
```bash
cd zenoh_services/web_gui
npm start
```

3. **访问系统**
- 打开浏览器访问: `http://localhost:3000`
- 默认会自动连接到后端服务

### 第一次训练

1. **选择环境**: 在Web界面中选择 "ZQSA01" 机器人环境
2. **配置训练**: 设置训练参数（默认设置适用于大多数情况）
3. **开始训练**: 点击"开始训练"按钮
4. **监控进度**: 查看实时训练曲线和机器人动作

## 🖥️ Web界面使用

### 主界面布局

```
┌─────────────────────────────────────────────────────────┐
│                    导航栏                                │
├─────────────────────────────────────────────────────────┤
│  侧边栏   │              主内容区域                      │
│  ┌─────┐  │  ┌─────────────────────────────────────┐    │
│  │仪表板│  │  │                                     │    │
│  │训练 │  │  │         当前页面内容                │    │
│  │仿真 │  │  │                                     │    │
│  │部署 │  │  │                                     │    │
│  │监控 │  │  │                                     │    │
│  └─────┘  │  └─────────────────────────────────────┘    │
│           │                                              │
├─────────────────────────────────────────────────────────┤
│                   状态栏                                 │
└─────────────────────────────────────────────────────────┘
```

### 仪表板 (Dashboard)

**系统状态概览**:
- 服务运行状态 (绿色=正常, 黄色=警告, 红色=错误)
- 资源使用情况 (CPU, GPU, 内存)
- 当前活动任务
- 系统健康评分

**快捷操作**:
- 开始/停止训练
- 启动/关闭仿真
- 查看最新日志
- 系统设置

### 训练页面 (Training)

#### 训练配置

```
┌─────────────────────────────────────┐
│             训练配置                │
├─────────────────────────────────────┤
│ 环境: ZQSA01                 [▼]    │
│ 算法: PPO                   [▼]    │
│ 设备: CUDA                  [▼]    │
│ 并行环境数: 4096            [___]  │
│ 最大迭代: 1000              [___]  │
│ 学习率: 0.0026              [___]  │
│ 批量大小: 24576             [___]  │
│                                     │
│ [高级设置] [保存配置] [开始训练]     │
└─────────────────────────────────────┘
```

**配置参数说明**:
- **环境**: 选择机器人模型和任务类型
- **算法**: 强化学习算法 (目前支持PPO)
- **设备**: 计算设备 (CUDA推荐)
- **并行环境数**: 同时运行的仿真环境数量
- **最大迭代**: 训练的最大轮数
- **学习率**: 神经网络学习率
- **批量大小**: 每次更新的样本数

#### 训练监控

**实时指标**:
```
奖励曲线 📈    损失函数 📉    成功率 📊
┌─────────┐   ┌─────────┐   ┌─────────┐
│ 平均奖励 │   │策略损失  │   │ 成功率  │
│  1250   │   │  0.123  │   │  78%   │
│         │   │         │   │        │
│  /\     │   │    \    │   │   /‾   │
│ /  \    │   │     \   │   │  /     │
│/    \_  │   │      \_ │   │ /      │
└─────────┘   └─────────┘   └─────────┘
```

**训练控制**:
- ⏸️ **暂停训练**: 暂停当前训练过程
- ⏹️ **停止训练**: 完全停止训练并保存模型
- 📊 **导出数据**: 导出训练数据和图表
- 🔄 **重启训练**: 从检查点重新开始

### 仿真页面 (Simulation)

#### 机器人控制面板

```
┌─────────────────────────────────────┐
│           机器人控制                │
├─────────────────────────────────────┤
│ 姿态控制:                           │
│ 前进速度: [-1.0] ──●── [1.0] m/s   │
│ 转向速度: [-1.0] ──●── [1.0] rad/s  │
│ 步行高度: [0.0]  ──●── [0.5] m     │
│                                     │
│ 动作模式:                           │
│ ○ 手动控制  ● AI控制  ○ 录制动作    │
│                                     │
│ [重置环境] [保存状态] [加载模型]     │
└─────────────────────────────────────┘
```

#### 仿真参数

```
┌─────────────────────────────────────┐
│           仿真设置                  │
├─────────────────────────────────────┤
│ 物理引擎: PhysX           [▼]      │
│ 时间步长: 0.0083s         [___]    │
│ 重力: -9.81 m/s²          [___]    │
│ 摩擦系数: 1.0             [___]    │
│                                     │
│ 渲染设置:                           │
│ ☑ 显示碰撞体  ☑ 显示关节            │
│ ☐ 慢动作模式  ☑ 实时渲染            │
│                                     │
│ [应用设置] [重置默认] [保存预设]     │
└─────────────────────────────────────┘
```

### 部署页面 (Deployment)

#### 模型转换

```
┌─────────────────────────────────────┐
│           模型部署                  │
├─────────────────────────────────────┤
│ 训练模型: model_1000.pt      [选择] │
│ 输出格式: ONNX               [▼]   │
│ 目标平台: NVIDIA Jetson      [▼]   │
│ 优化级别: 标准               [▼]   │
│                                     │
│ 模型信息:                           │
│ • 输入维度: (1, 235)                │
│ • 输出维度: (1, 12)                 │
│ • 参数数量: 1.2M                    │
│ • 模型大小: 4.8MB                   │
│                                     │
│ [转换模型] [测试模型] [下载模型]     │
└─────────────────────────────────────┘
```

#### 部署验证

```
┌─────────────────────────────────────┐
│           部署测试                  │
├─────────────────────────────────────┤
│ 测试状态: ● 运行中                  │
│ 推理延迟: 2.3ms                     │
│ 推理精度: 99.8%                     │
│ 内存使用: 126MB                     │
│                                     │
│ 测试结果:                           │
│ ✅ 模型加载成功                     │
│ ✅ 输入输出验证通过                 │
│ ✅ 性能测试通过                     │
│ ⚠️ 精度略有下降                     │
│                                     │
│ [生成报告] [重新测试] [部署到设备]   │
└─────────────────────────────────────┘
```

### 监控页面 (Monitoring)

#### 系统监控

```
┌─────────────────────────────────────┐
│           系统资源                  │
├─────────────────────────────────────┤
│ CPU:  [████████░░] 80%             │
│ GPU:  [██████████] 95%             │
│ 内存: [██████░░░░] 65%             │
│ 网络: ↑142MB/s ↓89MB/s             │
│                                     │
│ 服务状态:                           │
│ • 训练服务    ● 运行中              │
│ • 仿真服务    ● 运行中              │
│ • 部署服务    ⚠️ 警告               │
│ • 监控服务    ● 运行中              │
│                                     │
│ [详细信息] [导出日志] [系统报告]     │
└─────────────────────────────────────┘
```

#### 错误监控

```
┌─────────────────────────────────────┐
│           错误日志                  │
├─────────────────────────────────────┤
│ [错误] 2024-01-15 14:23:45         │
│ 训练服务: CUDA内存不足              │
│ 解决方案: 减少batch_size或env数量    │
│                                     │
│ [警告] 2024-01-15 14:20:12         │
│ 网络延迟过高: 120ms                 │
│ 建议: 检查网络连接状态              │
│                                     │
│ [信息] 2024-01-15 14:15:30         │
│ 模型保存成功: model_950.pt         │
│                                     │
│ [清空日志] [导出日志] [设置过滤器]   │
└─────────────────────────────────────┘
```

## 🎓 训练操作详解

### 训练流程

1. **环境准备**
```bash
# 检查系统状态
python scripts/check_system.py

# 清理GPU缓存
python -c "import torch; torch.cuda.empty_cache()"
```

2. **配置训练参数**
```python
# 编辑配置文件或通过Web界面设置
training_config = {
    "algorithm": "ppo",
    "num_envs": 4096,
    "max_iterations": 1000,
    "learning_rate": 0.0026,
    "batch_size": 24576,
    "entropy_coef": 0.01,
    "value_loss_coef": 1.0,
    "gamma": 0.99,
    "lam": 0.95
}
```

3. **开始训练**
```bash
# 命令行方式
python legged_gym/scripts/train.py --task=zqsa01 --headless

# 或通过Web界面点击"开始训练"
```

### 训练监控指标

**关键指标说明**:

- **Episode Length**: 每轮仿真的时长，越长表示机器人越稳定
- **Episode Reward**: 每轮获得的总奖励，应该随训练增加
- **Policy Loss**: 策略网络的损失，应该逐渐收敛
- **Value Loss**: 价值网络的损失，用于价值函数估计
- **Explained Variance**: 价值函数的解释方差，接近1表示估计准确

**训练阶段**:
1. **探索阶段** (0-200轮): 机器人随机探索，奖励波动大
2. **学习阶段** (200-600轮): 开始学会基本动作，奖励上升
3. **优化阶段** (600-1000轮): 精细调整动作，奖励稳定上升

### 超参数调优

**常用超参数**:

| 参数 | 默认值 | 调优建议 |
|------|--------|----------|
| learning_rate | 0.0026 | 训练不稳定时减小，收敛慢时增加 |
| num_envs | 4096 | 根据GPU内存调整 |
| batch_size | 24576 | 通常为num_envs的6倍 |
| gamma | 0.99 | 长期任务用0.99，短期任务用0.95 |
| entropy_coef | 0.01 | 增加探索时增大，过度探索时减小 |

**调优策略**:
1. 先用默认参数训练100-200轮观察表现
2. 如果奖励曲线平缓，增加学习率或熵系数
3. 如果训练不稳定，减少学习率或增加价值损失系数
4. 根据GPU内存调整环境数量

## 🎮 仿真控制

### 环境交互

**基本控制命令**:
```python
# 通过代码控制
from legged_gym.envs.zqsa01.zqsa01 import ZQSA01

env = ZQSA01(cfg=config, render=True)

# 执行动作
action = model(observation)
obs, reward, done, info = env.step(action)

# 重置环境
obs = env.reset()

# 手动控制
env.set_target_velocity(vx=1.0, vy=0.0, vyaw=0.0)
```

**Web界面控制**:
- 使用滑动条调整目标速度
- 切换控制模式 (手动/AI/录制)
- 实时查看机器人状态

### 仿真场景

**内置场景**:
1. **平地行走**: 在平坦地面上的基本行走
2. **斜坡行走**: 上下坡行走测试
3. **楼梯攀爬**: 台阶和楼梯环境
4. **不平地形**: 随机高度的地形
5. **障碍物**: 需要跨越或绕过障碍物

**自定义场景**:
```python
# 修改地形配置
class CustomTerrainConfig:
    mesh_type = 'trimesh'  # 'plane', 'heightfield', 'trimesh'
    static_friction = 1.0
    dynamic_friction = 1.0
    restitution = 0.0
    
    # 地形生成参数
    terrain_length = 8.0
    terrain_width = 8.0
    num_rows = 10
    num_cols = 20
    curriculum = True
    max_init_terrain_level = 5
```

## 📱 模型部署

### 模型导出

**ONNX导出**:
```python
# 命令行导出
python sim2real_deploy/export_onnx_policy.py \
    --checkpoint logs/trained_models/zqsa01/model_1000.pt \
    --output zqsa01_policy.onnx

# 或通过Web界面操作
```

**TensorRT优化** (可选):
```bash
# 安装TensorRT
pip install tensorrt

# 转换为TensorRT
trtexec --onnx=zqsa01_policy.onnx \
        --saveEngine=zqsa01_policy.trt \
        --fp16
```

### 目标平台部署

**NVIDIA Jetson**:
```python
# Jetson部署代码示例
import onnxruntime as ort
import numpy as np

# 创建推理会话
session = ort.InferenceSession(
    "zqsa01_policy.onnx",
    providers=['TensorrtExecutionProvider', 'CUDAExecutionProvider']
)

# 推理
def inference(observation):
    input_name = session.get_inputs()[0].name
    output = session.run(None, {input_name: observation})
    return output[0]
```

**Intel NUC/其他x86平台**:
```python
# CPU推理
session = ort.InferenceSession(
    "zqsa01_policy.onnx",
    providers=['CPUExecutionProvider']
)
```

### 部署验证

**功能测试**:
```bash
# 运行部署测试
python scripts/test_deployment.py \
    --model zqsa01_policy.onnx \
    --device jetson \
    --test-duration 60
```

**性能基准测试**:
```bash
# 推理延迟测试
python scripts/benchmark_inference.py \
    --model zqsa01_policy.onnx \
    --batch-size 1 \
    --iterations 1000
```

## 📊 监控和调试

### 实时监控

**系统资源监控**:
- CPU使用率和温度
- GPU利用率和显存
- 内存使用情况
- 网络I/O状态
- 磁盘空间

**训练监控**:
- 训练曲线实时更新
- 损失函数变化
- 梯度范数检查
- 学习率调度

**仿真监控**:
- 机器人姿态数据
- 关节角度和力矩
- 传感器读数
- 碰撞检测

### 调试工具

**日志分析**:
```bash
# 查看训练日志
tail -f logs/train_zqsa01.log

# 筛选错误日志
grep ERROR logs/train_zqsa01.log

# 分析性能日志
python scripts/analyze_logs.py --logfile logs/train_zqsa01.log
```

**可视化调试**:
```python
# 机器人状态可视化
from legged_gym.utils.visualization import RobotVisualizer

visualizer = RobotVisualizer()
visualizer.plot_joint_angles(joint_data)
visualizer.plot_trajectory(trajectory_data)
visualizer.plot_reward_components(reward_data)
```

**TensorBoard集成**:
```bash
# 启动TensorBoard
tensorboard --logdir logs/tensorboard_logs

# 访问 http://localhost:6006 查看训练图表
```

## 🛠️ 命令行工具

### 训练命令

```bash
# 基本训练
python legged_gym/scripts/train.py --task zqsa01

# 无头模式训练 (服务器)
python legged_gym/scripts/train.py --task zqsa01 --headless

# 从检查点恢复
python legged_gym/scripts/train.py --task zqsa01 --resume --checkpoint model_500.pt

# 自定义配置
python legged_gym/scripts/train.py --task zqsa01 --config custom_config.py
```

### 测试和播放

```bash
# 测试训练好的模型
python legged_gym/scripts/play.py --task zqsa01 --checkpoint model_1000.pt

# 录制视频
python legged_gym/scripts/play.py --task zqsa01 --checkpoint model_1000.pt --record

# 测试特定场景
python legged_gym/scripts/play.py --task zqsa01 --checkpoint model_1000.pt --terrain slopes
```

### 工具命令

```bash
# 系统检查
python scripts/check_system.py

# 配置验证
python scripts/validate_config.py --config config/system_config.yaml

# 数据清理
python scripts/cleanup_logs.py --older-than 7days

# 模型转换
python sim2real_deploy/export_onnx_policy.py --checkpoint model.pt --output model.onnx

# 性能基准
python scripts/benchmark.py --task zqsa01 --iterations 1000
```

### 服务管理

```bash
# 启动所有服务
python demo_services.py --start-all

# 启动特定服务
python demo_services.py --start training simulation

# 停止服务
python demo_services.py --stop-all

# 服务状态检查
python demo_services.py --status

# 重启服务
python demo_services.py --restart monitoring
```

## ❓ 常见问题

### 训练相关

**Q: 训练奖励不增长怎么办？**
A: 
1. 检查奖励函数设计是否合理
2. 尝试调整学习率 (0.001-0.01范围)
3. 增加熵系数促进探索
4. 检查动作范围是否合适

**Q: GPU内存不足怎么解决？**
A:
1. 减少并行环境数量 (num_envs)
2. 减少批量大小 (batch_size)  
3. 启用梯度检查点 (gradient_checkpointing)
4. 使用混合精度训练

**Q: 训练速度太慢怎么办？**
A:
1. 检查是否使用GPU加速
2. 增加批量大小提高并行度
3. 关闭不必要的渲染和记录
4. 使用更快的物理引擎设置

### 仿真相关

**Q: 机器人模型加载失败？**
A:
1. 检查URDF文件路径是否正确
2. 确认mesh文件存在且格式正确
3. 验证URDF语法是否正确
4. 检查材质和纹理文件

**Q: 仿真不稳定或爆炸？**
A:
1. 检查物理参数设置 (质量、惯性)
2. 调整时间步长 (减小dt值)
3. 增加求解器迭代次数
4. 检查关节限制和碰撞设置

### 部署相关

**Q: ONNX模型推理结果不一致？**
A:
1. 检查输入数据预处理
2. 验证模型转换精度
3. 确认运行时版本兼容性
4. 使用相同的随机种子

**Q: 部署设备性能不足？**
A:
1. 使用模型量化技术
2. 采用TensorRT等推理加速
3. 减少模型复杂度
4. 优化推理频率

---

**下一步**: 查看[API文档](API.md)了解程序接口详情。